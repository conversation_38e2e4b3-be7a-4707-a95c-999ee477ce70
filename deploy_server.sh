#!/bin/bash
# ============================================================================
# 服务器环境部署脚本 (Server Deployment Script)
# ============================================================================
#
# 本脚本用于在服务器环境中部署程序性视频理解系统
# 硬件要求：2张NVIDIA A6000 48GB显卡
# 操作系统：Linux (Ubuntu 18.04+)
#
# 使用方法：
# chmod +x deploy_server.sh
# ./deploy_server.sh
# ============================================================================

set -e  # 遇到错误立即退出

echo "🚀 开始服务器环境部署..."
echo "$(printf '=%.0s' {1..60})"

# ============================================================================
# 1. 环境检查
# ============================================================================
echo "🔍 检查系统环境..."

# 检查操作系统
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "❌ 错误：此脚本仅支持Linux系统"
    exit 1
fi

# 检查CUDA
if ! command -v nvidia-smi &> /dev/null; then
    echo "❌ 错误：未检测到NVIDIA GPU或驱动"
    exit 1
fi

# 检查GPU数量和型号
GPU_COUNT=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
GPU_NAMES=$(nvidia-smi --query-gpu=name --format=csv,noheader)

echo "✅ 检测到 $GPU_COUNT 个GPU："
echo "$GPU_NAMES"

# 检查是否为A6000
if ! echo "$GPU_NAMES" | grep -q "A6000"; then
    echo "❌ 错误：此配置专为A6000 GPU设计，当前硬件不支持"
    exit 1
fi

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未安装Python3"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
echo "✅ Python版本：$PYTHON_VERSION"

# 检查conda
if ! command -v conda &> /dev/null; then
    echo "❌ 错误：未安装Conda"
    echo "请先安装Miniconda或Anaconda"
    exit 1
fi

echo "✅ Conda已安装"

# ============================================================================
# 2. 数据路径检查
# ============================================================================
echo ""
echo "📁 检查数据路径..."

DATA_ROOT="/data2/syd_data/Breakfast_Data"
BREAK_FAST_DIR="$DATA_ROOT/breakfast_data"
LABEL_DIR="$DATA_ROOT/segmentation_coarse"
OUTPUT_DIR="$DATA_ROOT/Outputs"

# 检查数据根目录
if [ ! -d "$DATA_ROOT" ]; then
    echo "❌ 错误：数据根目录不存在：$DATA_ROOT"
    echo "请确保数据已正确上传到服务器"
    exit 1
fi

echo "✅ 数据根目录存在：$DATA_ROOT"

# 检查原始数据目录
if [ ! -d "$BREAK_FAST_DIR" ]; then
    echo "❌ 错误：原始数据目录不存在：$BREAK_FAST_DIR"
    echo "预期结构：$BREAK_FAST_DIR/{s1,s2,s3,s4}/"
    exit 1
fi

echo "✅ 原始数据目录存在：$BREAK_FAST_DIR"

# 检查视角目录
for view in s1 s2 s3 s4; do
    if [ ! -d "$BREAK_FAST_DIR/$view" ]; then
        echo "❌ 错误：视角目录不存在：$BREAK_FAST_DIR/$view"
        exit 1
    fi
    echo "✅ 视角目录存在：$view"
done

# 检查标签目录
if [ ! -d "$LABEL_DIR" ]; then
    echo "❌ 错误：标签目录不存在：$LABEL_DIR"
    echo "预期结构：$LABEL_DIR/{s1_label,s2_label,s3_label,s4_label}/"
    exit 1
fi

echo "✅ 标签目录存在：$LABEL_DIR"

# 检查标签子目录
for view in s1_label s2_label s3_label s4_label; do
    if [ ! -d "$LABEL_DIR/$view" ]; then
        echo "❌ 错误：标签子目录不存在：$LABEL_DIR/$view"
        exit 1
    fi
    echo "✅ 标签子目录存在：$view"
done

# 创建输出目录
mkdir -p "$OUTPUT_DIR"/{checkpoints,visualizations,reports,pictures}
echo "✅ 输出目录已创建：$OUTPUT_DIR"

# ============================================================================
# 3. 环境配置
# ============================================================================
echo ""
echo "🔧 配置Python环境..."

# 创建conda环境
ENV_NAME="sci_1"
if conda env list | grep -q "$ENV_NAME"; then
    echo "⚠️ 环境 $ENV_NAME 已存在，将重新创建"
    conda env remove -n "$ENV_NAME" -y
fi

echo "📦 创建conda环境：$ENV_NAME"
conda create -n "$ENV_NAME" python=3.8 -y

# 激活环境
echo "🔄 激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate "$ENV_NAME"

# 安装PyTorch (CUDA 11.8)
echo "📦 安装PyTorch (CUDA 11.8)..."
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

# 安装其他依赖
echo "📦 安装项目依赖..."
pip install -r requirements.txt

# 安装服务器专用依赖
echo "📦 安装服务器专用依赖..."
pip install tensorboard tensorboardX

# 可选：安装Flash Attention (如果可用)
echo "📦 尝试安装Flash Attention..."
pip install flash-attn --no-build-isolation || echo "⚠️ Flash Attention安装失败，将使用标准实现"

# 可选：安装APEX (如果可用)
echo "📦 尝试安装NVIDIA Apex..."
pip install apex || echo "⚠️ NVIDIA Apex安装失败，将使用标准优化器"

# ============================================================================
# 4. 验证安装
# ============================================================================
echo ""
echo "🧪 验证安装..."

# 验证PyTorch
python3 -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python3 -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
python3 -c "import torch; print(f'GPU数量: {torch.cuda.device_count()}')"

# 验证项目模块
echo "🔍 验证项目模块..."
python3 -c "import sys; sys.path.append('src'); import data_loader_fixed; print('✅ 数据加载器模块正常')"
python3 -c "import sys; sys.path.append('src'); import model; print('✅ 模型模块正常')"
python3 -c "import sys; sys.path.append('src'); import evaluation_simple; print('✅ 评估模块正常')"

# ============================================================================
# 5. 创建启动脚本
# ============================================================================
echo ""
echo "📝 创建启动脚本..."

# 创建训练启动脚本
cat > run_training_server.sh << 'EOF'
#!/bin/bash
# 服务器训练启动脚本

# 激活环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate sci_1

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1
export NCCL_DEBUG=INFO

echo "🚀 开始训练..."
echo "使用配置：configs/server_config.yaml"
echo "硬件配置：A6000双卡"

# 静态模型训练
echo "📈 训练静态模型..."
python scripts/train_Static.py --config=configs/server_config.yaml --amp --compile

# 差分模型训练
echo "📈 训练差分模型..."
python scripts/train_Diff.py --static-model=/data2/syd_data/Breakfast_Data/Outputs/checkpoints/static_model.pth --config=configs/server_config.yaml --amp --compile

echo "✅ 训练完成！"
echo "结果保存在：/data2/syd_data/Breakfast_Data/Outputs/"
EOF

chmod +x run_training_server.sh

# 创建多GPU训练脚本
cat > run_multi_gpu_training.sh << 'EOF'
#!/bin/bash
# 多GPU分布式训练启动脚本

# 激活环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate sci_1

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1
export NCCL_DEBUG=INFO

echo "🚀 开始多GPU分布式训练..."
echo "使用配置：configs/server_config.yaml"
echo "硬件配置：A6000双卡分布式"

# 静态模型多GPU训练
echo "📈 多GPU训练静态模型..."
torchrun --nproc_per_node=2 scripts/train_Static.py --config=configs/server_config.yaml --amp --compile

echo "✅ 多GPU训练完成！"
echo "结果保存在：/data2/syd_data/Breakfast_Data/Outputs/"
EOF

chmod +x run_multi_gpu_training.sh

# ============================================================================
# 6. 部署完成
# ============================================================================
echo ""
echo "🎉 服务器环境部署完成！"
echo "$(printf '=%.0s' {1..60})"
echo ""
echo "📋 部署摘要："
echo "  - 环境名称：$ENV_NAME"
echo "  - Python版本：$PYTHON_VERSION"
echo "  - GPU数量：$GPU_COUNT"
echo "  - 数据路径：$DATA_ROOT"
echo "  - 输出路径：$OUTPUT_DIR"
echo ""
echo "🚀 开始训练："
echo "  # 单GPU训练"
echo "  ./run_training_server.sh"
echo ""
echo "  # 多GPU分布式训练"
echo "  ./run_multi_gpu_training.sh"
echo ""
echo "  # 手动训练命令"
echo "  conda activate $ENV_NAME"
echo "  python scripts/train_Static.py --config=configs/server_config.yaml --amp --compile"
echo ""
echo "📊 查看结果："
echo "  ls $OUTPUT_DIR/checkpoints/     # 模型权重"
echo "  ls $OUTPUT_DIR/visualizations/  # 可视化图表"
echo "  ls $OUTPUT_DIR/reports/         # 训练报告"
echo ""
echo "✅ 部署成功！"
