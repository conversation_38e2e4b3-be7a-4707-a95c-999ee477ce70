# ============================================================================
# 程序性视频理解模型配置文件 (Procedural Video Understanding Configuration)
# ============================================================================
#
# 本配置文件用于配置基于任务图和原型学习的程序性视频理解模型
# 支持自动GPU优化，包括A6000专用优化和RTX 3060Ti优化
#
# 核心组件：
# 1. 静态任务图学习 (Static Task Graph Learning)
# 2. 动作原型特征学习 (Action Prototype Learning)
# 3. 多视角融合 (Multi-view Fusion)
# 4. 时序建模 (Temporal Modeling)
# 5. 自动GPU优化 (Automatic GPU Optimization)
#
# 适用数据集：50 Salads, Breakfast Actions, GTEA等程序性视频数据集
# 支持硬件：A6000 (48GB), RTX 3060Ti (8GB), 通用GPU, CPU
# ============================================================================

# ============================================================================
# 数据配置 (Data Configuration)
# ============================================================================
data:
  # 数据根目录路径 - 实际数据位置
  # 应包含以下结构：
  # breakfast_data/
  # ├── s1/                # 视角1的特征文件
  # │   ├── cereals/       # 动作类别
  # │   ├── coffee/
  # │   └── ...
  # ├── s2/                # 视角2的特征文件
  # ├── s3/                # 视角3的特征文件
  # └── s4/                # 视角4的特征文件
  root_dir: "/data2/syd_data/Breakfast_Data/breakfast_data"  # 数据根目录路径

  # 标签文件根目录路径 - 实际标签位置
  # 应包含以下结构：
  # segmentation_coarse/
  # ├── S1_label/          # 视角1的标签文件（注意大写）
  # │   ├── cereals/       # 动作类别
  # │   ├── coffee/
  # │   └── ...
  # ├── S2_label/          # 视角2的标签文件
  # ├── S3_label/          # 视角3的标签文件
  # └── S4_label/          # 视角4的标签文件
  label_root_dir: "/data2/syd_data/Breakfast_Data/segmentation_coarse"  # 标签文件根目录路径

  # 训练使用的视角列表
  # breakfast数据集包含4个视角：s1(stereo_01), s2(stereo_02), s3(webcam_01), s4(webcam_02)
  # 建议使用多个视角进行训练以提高模型的泛化能力
  train_views: ["s1", "s2", "s3"]

  # 测试使用的视角列表
  # 通常选择与训练视角不同的视角来评估跨视角泛化能力
  test_views: ["s4"]

  # 预提取特征的维度
  # 根据实际数据文件分析，当前数据集特征维度为65维
  # 确保与实际特征文件的维度一致
  feature_dim: 65

  # 最大序列长度（帧数）
  # 用于padding和截断序列，根据数据集中最长视频的长度设置
  # 当前cereals数据集视频长度约为800-900帧
  max_sequence_length: 1200

  # 动作类别总数
  # 数量将从实际数据中动态确定，无需在配置文件中指定
  # 完整Breakfast数据集通常包含多种动作类别，如：
  # SIL, add_saltnpepper, add_teabag, butter_pan, crack_egg, cut_bun, cut_fruit, cut_orange,
  # fry_egg, fry_pancake, peel_fruit, pour_cereals, pour_coffee, pour_dough2pan, pour_egg2pan,
  # pour_flour, pour_juice, pour_milk, pour_oil, pour_sugar, pour_water, put_bunTogether,
  # put_egg2plate, put_fruit2bowl, put_pancake2plate, put_toppingOnTop, smear_butter,
  # spoon_flour, spoon_powder, spoon_sugar, squeeze_orange, stir_cereals, stir_coffee,
  # stir_dough, stir_egg, stir_fruit, stir_milk, stir_tea, stirfry_egg, take_bowl, take_butter,
  # take_cup, take_eggs, take_glass, take_knife, take_plate, take_squeezer, take_topping

# ============================================================================
# 模型配置 (Model Configuration)
# ============================================================================
model:
  # ----------------------------------------------------------------------------
  # 静态任务图配置 (Static Task Graph Configuration)
  # ----------------------------------------------------------------------------
  # 任务图用于建模动作之间的依赖关系和时序约束
  # 简化版本：使用基础图神经网络，不使用注意力机制
  task_graph:
    # 图神经网络隐藏层维度
    # 控制任务图表示的复杂度，较大的值可以捕获更复杂的动作关系
    # 建议范围：256-1024，根据数据集复杂度调整
    hidden_dim: 512

    # 图神经网络层数
    # 控制信息传播的跳数，更多层可以捕获更远距离的动作依赖
    # 建议范围：2-5层，过多层可能导致过平滑问题
    num_gnn_layers: 3

    # Dropout概率
    # 防止过拟合，特别是在小数据集上训练时
    # 建议范围：0.1-0.3
    dropout: 0.1

    # 图池化方法
    # "mean": 简单平均池化（推荐）
    # "max": 最大值池化
    # "sum": 求和池化
    graph_pooling: "mean"

  # ----------------------------------------------------------------------------
  # 动作原型特征配置 (Action Prototype Configuration)
  # ----------------------------------------------------------------------------
  # 原型学习用于学习每个动作类别的典型特征表示
  prototype:
    # 原型特征维度
    # 控制原型表示的丰富程度，需要平衡表达能力和计算效率
    # 建议范围：128-512
    prototype_dim: 256

    # 每个动作类别的原型数量
    # 用于捕获动作的多样性（如不同执行方式、不同对象等）
    # 建议范围：1-5，过多可能导致过拟合
    num_prototypes_per_action: 3

    # 原型匹配的温度参数
    # 控制原型匹配的锐度，较小值使匹配更加尖锐
    # 建议范围：0.05-0.2
    temperature: 0.1

    # 原型更新的动量参数
    # 控制原型更新的平滑程度，较大值使更新更加平滑
    # 建议范围：0.9-0.999
    momentum: 0.999

  # ----------------------------------------------------------------------------
  # 多视角融合配置 (Multi-view Fusion Configuration)
  # ----------------------------------------------------------------------------
  # 用于融合来自不同视角的信息
  # 简化版本：使用简单的拼接或平均融合，不使用注意力机制
  fusion:
    # 视角编码器的隐藏维度
    # 用于将不同视角的特征映射到统一空间
    view_encoder_dim: 512

    # 融合方法
    # "concat": 简单拼接后通过线性层（推荐）
    # "mean": 简单平均融合
    # "gated": 门控融合机制
    fusion_method: "concat"

    # 是否启用域适应
    # 用于减少不同视角之间的域差异
    # 建议在跨视角评估时启用
    domain_adaptation: true

    # 是否启用不确定性加权
    # 根据模型对不同视角的置信度进行加权
    # 可以提高融合的鲁棒性
    uncertainty_weighting: false

# ============================================================================
# 训练配置 (Training Configuration)
# ============================================================================
training:
  # 批次大小 - 支持自动GPU优化 (已验证CUDA 11.8兼容性)
  # RTX 3060Ti (8GB显存): 24 (已优化)
  # A6000 (48GB显存): 单GPU 96, 双GPU 128 (自动调整)
  # 可根据实际GPU内存手动调整
  # 会被train.py自动调整
  batch_size: 32

  # 学习率
  # 控制参数更新的步长，需要根据模型复杂度和数据集大小调整
  # 建议范围：1e-4到1e-2，可以通过学习率调度器动态调整
  # 大批次训练时建议适当增加学习率
  learning_rate: 0.0003

  # 权重衰减（L2正则化）
  # 防止过拟合，特别是在参数较多的模型中
  # 建议范围：1e-5到1e-3
  weight_decay: 0.01

  # 训练轮数
  # 根据数据集大小和模型复杂度调整
  # 建议监控验证集性能，使用早停机制
  num_epochs: 10

  # 预热轮数
  # 在训练初期使用较小的学习率，有助于模型稳定收敛
  # 建议设置为总轮数的5-15%
  warmup_epochs: 10

  # 学习率调度器
  # "cosine": 余弦退火调度器，平滑降低学习率
  # "step": 阶梯式降低学习率
  # "plateau": 基于验证集性能的自适应调度器
  # "linear": 线性降低学习率
  scheduler: "cosine"

  # ----------------------------------------------------------------------------
  # 硬件配置 (Hardware Configuration)
  # ----------------------------------------------------------------------------
  # A6000双卡专用硬件优化配置
  hardware:
    # 是否启用TF32加速 (A6000专用特性)
    tf32: true

    # 是否启用模型编译 (torch.compile 优化)
    enable_compile: true

    # Flash Attention配置 (当前架构不适用)
    # 注意：本系统使用LSTM+简化融合，不包含注意力机制，此配置项无效
    flash_attention: false

  # ----------------------------------------------------------------------------
  # 损失函数权重 (Loss Function Weights)
  # ----------------------------------------------------------------------------
  # 多任务学习中各个损失项的权重，需要根据任务重要性和收敛速度调整
  loss_weights:
    # 动作分类损失权重（主要任务）
    # 这是核心任务，通常设置为1.0作为基准
    action_classification: 1.0

    # 原型对比学习损失权重
    # 用于学习判别性的动作原型，有助于提高分类性能
    # 建议范围：0.1-1.0
    prototype_contrastive: 0.5

    # 任务图结构损失权重
    # 用于学习合理的动作依赖关系，对程序性视频理解很重要
    # 建议范围：0.1-0.5
    task_graph_structure: 0.3

    # 时序一致性损失权重
    # 确保相邻帧的预测具有时序连贯性
    # 建议范围：0.1-0.5
    temporal_consistency: 0.2

    # 视角对齐损失权重
    # 用于多视角学习，减少视角间的差异
    # 建议范围：0.05-0.2
    view_alignment: 0.1

  # ----------------------------------------------------------------------------
  # GPU优化配置 (GPU Optimization Configuration)
  # ----------------------------------------------------------------------------
  # 专为A6000双卡配置优化 (完全兼容CUDA 11.8)
  # - A6000 (48GB×2): 自动应用高性能配置 (TF32, JIT, 多GPU)
  gpu_optimization:
    # 是否启用混合精度训练 (Automatic Mixed Precision)
    # 使用FP16可以减少显存使用并加速训练
    # A6000支持Tensor Core加速，显著提升性能
    enable_amp: true

    # 数据加载器工作进程数
    # A6000双卡配置优化：
    # - 双A6000: 16个worker (充分利用多核CPU)
    # - 单A6000: 8-12个worker
    num_workers: 16

    # 是否固定内存 (Pin Memory)
    # 加速CPU到GPU的数据传输，推荐启用
    pin_memory: true

    # 梯度累积步数
    # A6000双卡配置显存充足，通常设置为1
    gradient_accumulation_steps: 1

    # 最大显存使用比例
    # A6000: 0.98 (48GB显存充足，激进使用以最大化性能)
    max_memory_fraction: 0.98

    # 是否启用内存清理
    # 定期清理GPU缓存，防止内存泄漏
    enable_memory_cleanup: true

    # 内存清理频率（每多少个batch清理一次）
    # A6000: 150 (更频繁清理以维持最佳性能)
    memory_cleanup_freq: 150

    # 是否启用非阻塞传输
    enable_non_blocking: true

    # 是否启用torch.compile优化
    # 默认禁用，避免Triton依赖问题
    enable_compile: false

    # ----------------------------------------------------------------------------
    # A6000专用优化配置 (A6000 Specific Optimizations)
    # ----------------------------------------------------------------------------
    # 以下配置仅在检测到A6000时自动启用 (已验证CUDA 11.8兼容性)
    a6000_optimizations:
      # 是否启用TF32加速 (已验证CUDA 11.8兼容)
      # A6000支持TensorFloat-32，可显著提升矩阵运算性能
      enable_tf32: true

      # 是否启用JIT编译优化 (已验证CUDA 11.8兼容)
      # 对静态图模型有显著性能提升
      enable_jit_compile: true

      # 是否启用channels_last内存格式 (已验证CUDA 11.8兼容)
      # 对卷积操作有性能提升
      enable_channels_last: true

      # 多GPU并行配置
      multi_gpu:
        # 并行后端：nccl (推荐), gloo
        backend: "nccl"

        # 是否查找未使用的参数
        # 设为false可提升性能
        find_unused_parameters: false

        # 梯度桶大小 (MB)
        # 控制梯度通信的粒度
        bucket_cap_mb: 25

      # 数据加载器优化
      dataloader:
        # 是否启用持久化worker
        # A6000显存充足，可以启用以减少worker重启开销
        persistent_workers: true

        # 预取因子
        # 控制每个worker预取的batch数量
        prefetch_factor: 4

# ============================================================================
# 评估配置 (Evaluation Configuration)
# ============================================================================
evaluation:
  # ----------------------------------------------------------------------------
  # 评估指标 (Evaluation Metrics)
  # ----------------------------------------------------------------------------
  # 用于评估模型性能的指标列表
  metrics:
    # 帧级别准确率
    # 计算每一帧预测的准确率，是最基础的评估指标
    - "frame_accuracy"

    # 段级别准确率
    # 计算动作段的准确率，更关注动作边界的准确性
    - "segment_accuracy"

    # 编辑距离（Levenshtein距离）
    # 衡量预测序列与真实序列之间的差异，越小越好
    - "edit_distance"

    # F1分数
    # 综合考虑精确率和召回率的调和平均数
    - "f1_score"

    # 图相似性
    # 评估学习到的任务图与真实任务图的相似程度
    - "graph_similarity"

    # 原型质量
    # 评估学习到的动作原型的判别性和代表性
    - "prototype_quality"

  # ----------------------------------------------------------------------------
  # 可视化配置 (Visualization Configuration)
  # ----------------------------------------------------------------------------
  visualization:
    # 是否保存原型聚类图
    # 用于分析学习到的动作原型的分布和聚类效果
    save_prototype_clusters: true

    # 是否保存任务图
    # 用于可视化学习到的动作依赖关系
    save_task_graphs: true

# ============================================================================
# 实验配置 (Experiment Configuration)
# ============================================================================
experiment:
  # 实验名称
  # 用于标识不同的实验配置，建议使用描述性名称
  # 格式建议：数据集_模型特点_日期
  name: "breakfast_task_graph"

  # 注意：输出目录统一使用服务器路径 /data2/syd_data/Breakfast_Data/Outputs/
  # 包含以下子目录：
  # - checkpoints/: 模型检查点
  # - visualizations/: 图片和可视化
  # - reports/: 实验报告
  # 不再使用单独的experiments目录以避免路径混乱

  # 模型检查点保存频率（轮数）
  # 每隔多少轮保存一次模型检查点
  # 建议根据训练时间和存储空间调整
  checkpoint_freq: 10

  # 日志记录频率（批次）
  # 每隔多少个批次记录一次训练日志
  # 较小的值提供更详细的训练过程监控
  log_freq: 100

  # 随机种子
  # 用于确保实验的可重复性
  # 固定种子可以使多次运行得到相同的结果
  seed: 42

# ============================================================================
# 配置文件使用说明
# ============================================================================
#
# 1. 数据集适配：
#    - 修改 data.root_dir 为实际数据路径
#    - 根据数据集调整 data.num_actions
#    - 确认 data.feature_dim 与预提取特征维度一致
#
# 2. 自动GPU优化：
#    - 系统会自动检测GPU类型并应用相应优化
#    - A6000: 自动启用高性能配置 (batch_size=96/128, TF32, JIT等)
#    - RTX 3060Ti: 自动启用内存优化配置 (batch_size=24, 频繁内存清理)
#    - 其他GPU: 使用通用配置
#    - 无需手动修改GPU相关配置，系统会自动处理
#
# 3. 手动性能调优（可选）：
#    - 调整 training.batch_size 覆盖自动配置
#    - 调整 model.task_graph.hidden_dim 控制模型复杂度
#    - 调整 training.loss_weights 平衡不同任务
#    - 调整 model.prototype.num_prototypes_per_action 控制原型数量
#
# 4. 实验管理：
#    - 修改 experiment.name 标识不同实验
#    - 调整 experiment.checkpoint_freq 控制存储频率
#
# 5. GPU优化特性：
#    - 支持多GPU并行训练 (A6000)
#    - 自动混合精度训练 (AMP)
#    - TF32加速 (A6000)
#    - JIT编译优化 (A6000)
#    - 智能内存管理
#    - 自动配置切换
#
# 6. 性能预期：
#    - 单A6000 vs RTX 3060Ti: 2-3x性能提升
#    - 双A6000 vs RTX 3060Ti: 4-5x性能提升
#    - 训练时间节省: 60-75%
#
# 注意：本配置支持完整的GPU自动优化功能
# 适用于生产环境和高性能训练需求
#
# ============================================================================
