"""
修复的数据加载器
适配实际的breakfast数据集结构
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple


class BreakfastDataset(Dataset):
    """Breakfast数据集加载器"""

    def __init__(self,
                 root_dir: str,
                 views: List[str],
                 max_sequence_length: int = 1000,
                 feature_dim: int = 65,
                 is_training: bool = True,
                 label_root_dir: str = "data/segmentation_coarse",
                 target_tasks: List[str] = None):
        """
        Args:
            root_dir: 数据根目录
            views: 视角列表 (e.g., ['s1', 's2', 's3'])
            max_sequence_length: 最大序列长度
            feature_dim: 特征维度
            is_training: 是否为训练模式
            label_root_dir: 标签文件根目录，如果为None则使用默认路径
            target_tasks: 指定要加载的任务列表，如果为None则加载所有任务
        """
        self.root_dir = root_dir
        self.views = views
        self.max_sequence_length = max_sequence_length
        self.feature_dim = feature_dim
        self.is_training = is_training
        self.label_root_dir = label_root_dir
        self.target_tasks = target_tasks

        # 加载数据样本
        self.data_samples = self._load_data_samples()

        # 构建动作映射
        self.action_to_id, self.id_to_action = self._build_action_mapping()

        print(f"加载了 {len(self.data_samples)} 个样本")
        print(f"动作类别: {list(self.action_to_id.keys())}")

    def _load_data_samples(self) -> List[Dict]:
        """加载数据样本 - 支持灵活的目录结构"""
        samples = []

        for view in self.views:
            # 特征文件的视角目录
            view_feature_dir = os.path.join(self.root_dir, view)

            if not os.path.exists(view_feature_dir):
                print(f"❌ 视角目录不存在: {view_feature_dir}")
                continue

            print(f"✅ 找到视角目录: {view_feature_dir}")

            # 尝试多种可能的标签文件目录结构
            possible_label_dirs = []
            if self.label_root_dir:
                # 使用配置的标签根目录，适配实际的目录结构
                if view.lower().startswith('s'):
                    # 实际数据使用大写的S1_label格式
                    possible_label_dirs.extend([
                        os.path.join(self.label_root_dir, f'S{view[1:]}_label'),  # S1_label (实际格式)
                        os.path.join(self.label_root_dir, f'{view.upper()}_label'),  # S1_label
                        os.path.join(self.label_root_dir, f'{view}_label'),  # s1_label
                        os.path.join(self.label_root_dir, view),  # s1
                    ])
                else:
                    possible_label_dirs.extend([
                        os.path.join(self.label_root_dir, f'{view.upper()}_label'),
                        os.path.join(self.label_root_dir, view),
                    ])
            else:
                # 使用默认路径（向后兼容）
                if view.lower().startswith('s'):
                    possible_label_dirs.extend([
                        os.path.join('data', 'segmentation_coarse', f'S{view[1:]}_label'),
                        os.path.join('data', 'segmentation_coarse', f'{view.upper()}_label'),
                        os.path.join('data', 'segmentation_coarse', f'{view}_label'),
                    ])
                else:
                    possible_label_dirs.extend([
                        os.path.join('data', 'segmentation_coarse', f'{view.upper()}_label'),
                    ])

            label_dir = None
            for dir_path in possible_label_dirs:
                if os.path.exists(dir_path):
                    label_dir = dir_path
                    print(f"✅ 找到标签目录: {label_dir}")
                    break

            if label_dir is None:
                print(f"❌ 未找到标签目录，尝试过的路径:")
                for dir_path in possible_label_dirs[:5]:  # 只显示前5个避免输出过长
                    print(f"   - {dir_path}")
                if len(possible_label_dirs) > 5:
                    print(f"   ... 还有 {len(possible_label_dirs) - 5} 个路径")
                continue

            # 遍历动作类别目录
            if os.path.isdir(view_feature_dir):
                action_dirs = [d for d in os.listdir(view_feature_dir) if os.path.isdir(os.path.join(view_feature_dir, d))]
                print(f"📁 在 {view_feature_dir} 中找到动作类别: {action_dirs}")

                for action_dir in action_dirs:
                    # 如果指定了target_tasks，只加载指定的任务
                    if self.target_tasks is not None and action_dir not in self.target_tasks:
                        continue

                    action_feature_dir = os.path.join(view_feature_dir, action_dir)
                    action_label_dir = os.path.join(label_dir, action_dir)

                    if not os.path.exists(action_label_dir):
                        print(f"⚠️ 动作标签目录不存在: {action_label_dir}")
                        continue

                    # 遍历该动作类别下的特征文件
                    feature_files = [f for f in os.listdir(action_feature_dir) if f.endswith('.txt')]
                    print(f"   📄 {action_dir}: {len(feature_files)} 个特征文件")

                    for feature_file in feature_files:
                        # 查找对应的标签文件
                        label_file = os.path.join(action_label_dir, feature_file)
                        if not os.path.exists(label_file):
                            print(f"⚠️ 标签文件不存在: {label_file}")
                            continue

                        feature_path = os.path.join(action_feature_dir, feature_file)

                        # 解析标签
                        segments = self._parse_label_file(label_file)

                        if segments:  # 只添加有标签的样本
                            sample = {
                                'view': view,
                                'action_category': action_dir,
                                'feature_path': feature_path,
                                'label_path': label_file,
                                'video_name': feature_file.replace('.txt', ''),
                                'segments': segments
                            }
                            samples.append(sample)

                    if len(feature_files) > 0:
                        print(f"✅ 加载 {action_dir}: {len([s for s in samples if s.get('action_category') == action_dir and s.get('view') == view])} 个样本")
            else:
                print(f"❌ 特征目录不是有效目录: {view_feature_dir}")

        return samples

    def _parse_label_file(self, label_file: str) -> List[Dict]:
        """解析标签文件"""
        segments = []

        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    # 解析格式: "1-30 SIL"
                    parts = line.split()
                    if len(parts) >= 2:
                        frame_range = parts[0]
                        action_name = parts[1]

                        if '-' in frame_range:
                            start_str, end_str = frame_range.split('-')
                            try:
                                start_frame = int(start_str) - 1  # 转换为0-based索引
                                end_frame = int(end_str) - 1

                                segments.append({
                                    'start': start_frame,
                                    'end': end_frame,
                                    'action': action_name
                                })
                            except ValueError:
                                continue

        except Exception as e:
            print(f"解析标签文件 {label_file} 时出错: {e}")

        return segments

    def _build_action_mapping(self) -> Tuple[Dict[str, int], Dict[int, str]]:
        """构建动作名称到ID的映射"""
        all_actions = set()

        for sample in self.data_samples:
            for segment in sample['segments']:
                all_actions.add(segment['action'])

        # 确保有一致的排序
        sorted_actions = sorted(all_actions)
        action_to_id = {action: idx for idx, action in enumerate(sorted_actions)}
        id_to_action = {idx: action for action, idx in action_to_id.items()}

        return action_to_id, id_to_action

    def _load_features(self, feature_path: str) -> np.ndarray:
        """加载特征文件"""
        try:
            features = np.loadtxt(feature_path)
            if len(features.shape) == 1:
                features = features.reshape(1, -1)
            return features.astype(np.float32)
        except Exception as e:
            # 【修复】移除静默失败机制，直接抛出异常强制用户检查数据完整性
            raise ValueError(f"无法加载特征文件 {feature_path}: {e}. "
                           f"请检查文件是否存在、格式是否正确、权限是否足够。"
                           f"数据完整性对训练结果至关重要，不允许使用随机数据替代。") from e

    def _generate_frame_labels(self, num_frames: int, segments: List[Dict]) -> np.ndarray:
        """根据时序标注生成帧级别标签"""
        frame_labels = np.full(num_frames, -1, dtype=np.int64)  # -1表示未标注

        for segment in segments:
            start = max(0, min(segment['start'], num_frames - 1))
            end = max(0, min(segment['end'], num_frames - 1))
            action_id = self.action_to_id.get(segment['action'], 0)

            if start <= end:
                frame_labels[start:end+1] = action_id

        return frame_labels

    def __len__(self) -> int:
        return len(self.data_samples)

    def __getitem__(self, idx: int) -> Dict:
        sample = self.data_samples[idx]

        # 加载特征
        features = self._load_features(sample['feature_path'])

        # 生成帧级别标签
        frame_labels = self._generate_frame_labels(features.shape[0], sample['segments'])

        # 记录原始长度
        original_length = features.shape[0]

        # 截断或填充到固定长度
        if features.shape[0] > self.max_sequence_length:
            features = features[:self.max_sequence_length]
            frame_labels = frame_labels[:self.max_sequence_length]
            sequence_length = self.max_sequence_length
        else:
            sequence_length = features.shape[0]
            # 填充
            pad_length = self.max_sequence_length - features.shape[0]
            features = np.pad(features, ((0, pad_length), (0, 0)), mode='constant')
            frame_labels = np.pad(frame_labels, (0, pad_length), mode='constant', constant_values=-1)

        return {
            'features': torch.FloatTensor(features),
            'labels': torch.LongTensor(frame_labels),
            'view': sample['view'],
            'video_name': sample['video_name'],
            'segments': sample['segments'],
            'sequence_length': torch.tensor(sequence_length, dtype=torch.long),
            'original_length': original_length
        }


def collate_fn(batch):
    """自定义的批处理函数"""
    # 简单地返回批次，因为我们已经在__getitem__中处理了填充
    return {
        'features': torch.stack([item['features'] for item in batch]),
        'labels': torch.stack([item['labels'] for item in batch]),
        'view': [item['view'] for item in batch],
        'video_name': [item['video_name'] for item in batch],
        'segments': [item['segments'] for item in batch],
        'sequence_length': torch.stack([item['sequence_length'] for item in batch]),
        'original_length': [item['original_length'] for item in batch]
    }


def _analyze_dataset_memory_requirements(dataset, max_samples=10):
    """分析数据集的内存需求，返回统计信息"""
    if len(dataset) == 0:
        return {"max_length": 0, "avg_length": 0, "memory_factor": 1.0}

    sample_count = min(max_samples, len(dataset))
    lengths = []

    for i in range(sample_count):
        try:
            sample = dataset[i]
            lengths.append(sample['sequence_length'].item())
        except Exception:
            continue

    if not lengths:
        return {"max_length": 0, "avg_length": 0, "memory_factor": 1.0}

    max_length = max(lengths)
    avg_length = sum(lengths) / len(lengths)

    # 计算内存因子：相对于平均长度的内存需求倍数
    memory_factor = max_length / avg_length if avg_length > 0 else 1.0

    return {
        "max_length": max_length,
        "avg_length": avg_length,
        "memory_factor": memory_factor,
        "sample_count": len(lengths)
    }


def create_data_loaders(config: Dict, worker_init_fn=None, **dl_kwargs) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和测试数据加载器，支持智能批次大小调整和分布式训练

    特性：
    - 智能批次大小调整：分析训练集和测试集的序列长度分布
    - 预防性调整：根据数据集特征预先调整批次大小
    - 双重验证：同时在训练集和测试集上验证批次大小
    - CUDA内存溢出自动恢复：自动减半批次大小直到成功
    - 分布式训练支持：自动检测并配置分布式采样器

    Args:
        config: 配置字典，包含数据和训练参数
        **dl_kwargs: 数据加载器的额外参数

    Returns:
        Tuple[DataLoader, DataLoader]: 训练和测试数据加载器
    """
    import math
    import os
    from torch.utils.data.distributed import DistributedSampler

    # 获取标签根目录
    label_root_dir = config['data'].get('label_root_dir', None)

    # 获取目标任务列表
    target_tasks = config['data'].get('target_tasks', None)

    # 训练数据集
    train_dataset = BreakfastDataset(
        root_dir=config['data']['root_dir'],
        views=config['data']['train_views'],
        max_sequence_length=config['data']['max_sequence_length'],
        feature_dim=config['data']['feature_dim'],
        is_training=True,
        label_root_dir=label_root_dir,
        target_tasks=target_tasks
    )

    # 测试数据集
    test_dataset = BreakfastDataset(
        root_dir=config['data']['root_dir'],
        views=config['data']['test_views'],
        max_sequence_length=config['data']['max_sequence_length'],
        feature_dim=config['data']['feature_dim'],
        is_training=False,
        label_root_dir=label_root_dir,
        target_tasks=target_tasks
    )

    # 打印数据集统计信息
    print(f"\n📊 数据集统计信息:")
    print(f"训练集:")
    print(f"  - 样本数量: {len(train_dataset)}")
    if len(train_dataset) > 0:
        print(f"  - 动作类别数: {len(train_dataset.action_to_id)}")
        print(f"  - 动作类别: {list(train_dataset.action_to_id.keys())}")
    print(f"  - 特征维度: {train_dataset.feature_dim}")
    print(f"  - 视角: {config['data']['train_views']}")

    print(f"测试集:")
    print(f"  - 样本数量: {len(test_dataset)}")
    if len(test_dataset) > 0:
        print(f"  - 动作类别数: {len(test_dataset.action_to_id)}")
    print(f"  - 视角: {config['data']['test_views']}")

    # 【修复】增强空数据集检测，提供更详细的错误信息
    if len(train_dataset) == 0:
        error_msg = "训练数据集为空！请检查以下配置："
        error_msg += f"\n  - 数据根目录: {config['data']['root_dir']}"
        error_msg += f"\n  - 训练视角: {config['data']['train_views']}"
        if target_tasks:
            error_msg += f"\n  - 目标任务: {target_tasks}"
            error_msg += f"\n  - 提示: 检查target_tasks中的任务名称是否在数据目录中存在"
        else:
            error_msg += f"\n  - 目标任务: 所有任务（未指定target_tasks）"
        if label_root_dir:
            error_msg += f"\n  - 标签根目录: {label_root_dir}"
        error_msg += f"\n请确保数据路径正确且包含有效的特征文件和标签文件。"
        raise ValueError(error_msg)

    if len(test_dataset) == 0:
        warning_msg = "警告: 测试数据集为空！"
        warning_msg += f"\n  - 数据根目录: {config['data']['root_dir']}"
        warning_msg += f"\n  - 测试视角: {config['data']['test_views']}"
        if target_tasks:
            warning_msg += f"\n  - 目标任务: {target_tasks}"
        print(warning_msg)

    # 获取GPU优化配置
    gpu_config = config.get('training', {}).get('gpu_optimization', {})
    num_workers = dl_kwargs.get('num_workers', gpu_config.get('num_workers', config['training'].get('num_workers', 6)))
    pin_memory = dl_kwargs.get('pin_memory', gpu_config.get('pin_memory', True))
    persistent_workers = dl_kwargs.get('persistent_workers', gpu_config.get('persistent_workers', True))
    prefetch_factor = dl_kwargs.get('prefetch_factor', gpu_config.get('prefetch_factor', 3))

    # 分析数据集内存需求
    print("🔍 分析数据集内存需求...")
    train_stats = _analyze_dataset_memory_requirements(train_dataset)
    test_stats = _analyze_dataset_memory_requirements(test_dataset)

    print(f"📊 训练集统计: 平均长度={train_stats['avg_length']:.1f}, 最大长度={train_stats['max_length']}, 内存因子={train_stats['memory_factor']:.2f}")
    print(f"📊 测试集统计: 平均长度={test_stats['avg_length']:.1f}, 最大长度={test_stats['max_length']}, 内存因子={test_stats['memory_factor']:.2f}")

    # 根据数据集特征调整初始批次大小
    max_memory_factor = max(train_stats['memory_factor'], test_stats['memory_factor'])
    if max_memory_factor > 2.0:
        # 如果数据集中有很长的序列，预先减小批次大小
        adjustment_factor = min(0.7, 2.0 / max_memory_factor)
        original_batch_size = config['training']['batch_size']
        config['training']['batch_size'] = max(1, int(original_batch_size * adjustment_factor))
        print(f"⚠️ 检测到长序列数据，预调整批次大小: {original_batch_size} → {config['training']['batch_size']}")

    # 动态批次大小调整（显存不足自动减半）
    # 同时考虑训练集和测试集的内存需求
    batch_size = config['training']['batch_size']
    print(f"🔧 尝试批次大小: {batch_size}")

    while batch_size >= 1:
        try:
            # 根据num_workers调整参数
            dataloader_kwargs = {
                'batch_size': batch_size,
                'pin_memory': pin_memory,
                'collate_fn': collate_fn,
                'num_workers': num_workers
            }

            # 只有在多进程模式下才设置这些参数
            if num_workers > 0:
                dataloader_kwargs['persistent_workers'] = persistent_workers
                dataloader_kwargs['prefetch_factor'] = prefetch_factor
                if worker_init_fn is not None:
                    dataloader_kwargs['worker_init_fn'] = worker_init_fn

            # 创建训练集测试加载器来验证批次大小
            train_test_loader = DataLoader(
                train_dataset,
                shuffle=False,
                drop_last=True,
                **dataloader_kwargs
            )

            # 创建测试集测试加载器来验证批次大小
            test_test_loader = DataLoader(
                test_dataset,
                shuffle=False,
                drop_last=True,
                **dataloader_kwargs
            )

            # 尝试加载训练集的一个批次来测试显存
            train_batch = next(iter(train_test_loader))
            print(f"✅ 训练集批次大小 {batch_size} 验证成功")

            # 尝试加载测试集的一个批次来测试显存
            test_batch = next(iter(test_test_loader))
            print(f"✅ 测试集批次大小 {batch_size} 验证成功")

            # 如果两个都成功，跳出循环
            print(f"✅ 批次大小 {batch_size} 在训练集和测试集上都验证成功")
            break

        except RuntimeError as e:
            if "CUDA out of memory" in str(e) and batch_size > 1:
                batch_size = math.ceil(batch_size / 2)
                print(f"⚠️ 显存不足，自动减小批次大小到: {batch_size}")
                torch.cuda.empty_cache()
            else:
                raise e
        except Exception as e:
            print(f"⚠️ 批次大小验证失败: {e}")
            if batch_size > 1:
                batch_size = math.ceil(batch_size / 2)
                print(f"⚠️ 自动减小批次大小到: {batch_size}")
            else:
                raise e

    # 更新配置中的批次大小
    config['training']['batch_size'] = batch_size

    # 重新构建数据加载器参数
    dataloader_kwargs = {
        'batch_size': batch_size,
        'pin_memory': pin_memory,
        'collate_fn': collate_fn,
        'num_workers': num_workers
    }

    # 只有在多进程模式下才设置这些参数
    if num_workers > 0:
        dataloader_kwargs['persistent_workers'] = persistent_workers
        dataloader_kwargs['prefetch_factor'] = prefetch_factor
        if worker_init_fn is not None:
            dataloader_kwargs['worker_init_fn'] = worker_init_fn
            print(f"✅ 多进程数据加载: {num_workers} workers, prefetch_factor={prefetch_factor}, worker_init_fn=已设置")
        else:
            print(f"✅ 多进程数据加载: {num_workers} workers, prefetch_factor={prefetch_factor}")
    else:
        print(f"✅ 单线程数据加载: num_workers=0 (避免多进程问题)")

    print(f"✅ 最终批次大小: {batch_size}")

    # 检查是否启用分布式训练
    is_distributed = 'LOCAL_RANK' in os.environ or 'RANK' in os.environ

    if is_distributed:
        print("🔄 检测到分布式训练环境，启用DistributedSampler")
        # 创建分布式采样器
        train_sampler = DistributedSampler(train_dataset, shuffle=True)
        test_sampler = DistributedSampler(test_dataset, shuffle=False)

        # 分布式训练时不能使用shuffle参数
        train_loader = DataLoader(
            train_dataset,
            sampler=train_sampler,
            drop_last=True,  # 丢弃最后不完整的batch以保持一致性
            **dataloader_kwargs
        )

        test_loader = DataLoader(
            test_dataset,
            sampler=test_sampler,
            drop_last=False,  # 测试时保留所有数据
            **dataloader_kwargs
        )

        # 将采样器添加到返回值中，以便训练脚本可以在每个epoch设置epoch
        train_loader.sampler = train_sampler
        test_loader.sampler = test_sampler

    else:
        print("🔄 单GPU训练模式")
        # 创建标准数据加载器
        train_loader = DataLoader(
            train_dataset,
            shuffle=True,
            drop_last=True,  # 丢弃最后不完整的batch以保持一致性
            **dataloader_kwargs
        )

        test_loader = DataLoader(
            test_dataset,
            shuffle=False,
            drop_last=False,  # 测试时保留所有数据
            **dataloader_kwargs
        )

    return train_loader, test_loader


if __name__ == "__main__":
    # 测试数据加载器
    import yaml

    # 修复配置文件路径
    config_path = os.path.join(os.path.dirname(__file__), '..', 'configs', 'default.yaml')
    if not os.path.exists(config_path):
        # 尝试从当前工作目录查找
        config_path = os.path.join('configs', 'default.yaml')

    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    try:
        train_loader, test_loader = create_data_loaders(config)

        print(f"训练集样本数: {getattr(train_loader.dataset, '__len__', lambda: '未知')()}")
        print(f"测试集样本数: {getattr(test_loader.dataset, '__len__', lambda: '未知')()}")

        if len(train_loader) > 0:
            # 测试一个batch
            for batch in train_loader:
                print(f"特征形状: {batch['features'].shape}")
                print(f"标签形状: {batch['labels'].shape}")
                print(f"视角: {batch['view']}")
                print(f"序列长度: {batch['sequence_length']}")
                break
    except Exception as e:
        print(f"测试失败: {e}")
