#!/usr/bin/env python3
"""
报告生成模块
统一的训练和推理报告生成工具
"""

import os
import sys
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# 添加根目录到Python路径
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib not available for visualization")

try: 
    from src.font_config import setup_chinese_font
    FONT_CONFIG_AVAILABLE = True
except ImportError:
    FONT_CONFIG_AVAILABLE = False

# 输出目录配置 - Linux服务器环境专用
def get_output_dirs():
    """获取输出目录，可通过环境变量 ``OUTPUT_DIR`` 覆盖默认值"""
    output_dir = Path(os.environ.get("OUTPUT_DIR", "/data2/syd_data/Breakfast_Data/Outputs"))
    return output_dir, output_dir / "reports", output_dir / "pictures"

OUTPUT_DIR, REPORT_DIR, PICTURE_DIR = get_output_dirs()


def setup_matplotlib_chinese():
    """设置matplotlib中文字体支持"""
    try:
        from font_config import setup_chinese_font
        setup_chinese_font()
    except ImportError:
        print("⚠️ 中文字体配置模块不可用，使用默认字体")


def save_plot(filename: str, fig: plt.Figure) -> Path:
    """
    保存图表到统一目录

    Args:
        filename: 文件名
        fig: matplotlib图表对象

    Returns:
        保存路径
    """
    try:
        from picture_utils import save_figure
        saved_path_str = save_figure(filename, fig=fig)
        return Path(saved_path_str)
    except ImportError:
        # 回退到直接保存
        PICTURE_DIR.mkdir(parents=True, exist_ok=True)
        save_path = PICTURE_DIR / filename
        fig.savefig(save_path, dpi=300, bbox_inches='tight')
        return save_path


class TrainingReportGenerator:
    """训练报告生成器"""
    
    def __init__(self, model_type: str = "static"):
        """
        初始化报告生成器
        
        Args:
            model_type: 模型类型 ("static" 或 "differential")
        """
        self.model_type = model_type
        REPORT_DIR.mkdir(parents=True, exist_ok=True)
        PICTURE_DIR.mkdir(parents=True, exist_ok=True)
    
    def generate_training_curves(self, train_history: Dict, profile: str) -> Path:
        """
        生成训练曲线图
        
        Args:
            train_history: 训练历史数据
            profile: 硬件配置文件
            
        Returns:
            图表保存路径
        """
        setup_matplotlib_chinese()
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        epochs = train_history["epoch"]
        
        # 1. 训练损失曲线
        ax1.plot(epochs, train_history["train_loss"], 'b-', label='训练损失', linewidth=2)
        ax1.set_title(f'{self.model_type.title()}模型训练损失曲线', fontsize=14, fontweight='bold')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 2. 准确率曲线
        ax2.plot(epochs, train_history["train_accuracy"], 'g-', label='训练准确率', linewidth=2)
        ax2.plot(epochs, train_history["test_accuracy"], 'r-', label='测试准确率', linewidth=2)
        ax2.set_title(f'{self.model_type.title()}模型准确率曲线', fontsize=14, fontweight='bold')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('准确率')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 3. 性能提升趋势
        if len(train_history["test_accuracy"]) > 1:
            improvement = [train_history["test_accuracy"][i] - train_history["test_accuracy"][0] 
                          for i in range(len(train_history["test_accuracy"]))]
            ax3.plot(epochs, improvement, 'purple', linewidth=2, marker='o')
            ax3.set_title('性能提升趋势', fontsize=14, fontweight='bold')
            ax3.set_xlabel('训练轮数')
            ax3.set_ylabel('准确率提升')
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # 4. 训练统计信息
        ax4.axis('off')

        # 修复：添加空列表检查，避免异常
        best_accuracy = max(train_history["test_accuracy"]) if train_history["test_accuracy"] else 0.0
        final_loss = train_history["train_loss"][-1] if train_history["train_loss"] else 0.0
        convergence_epoch = epochs[train_history["test_accuracy"].index(max(train_history["test_accuracy"]))] if train_history["test_accuracy"] else 'N/A'

        stats_text = f"""训练统计信息

最佳准确率: {best_accuracy:.4f}
最终损失: {final_loss:.4f}
训练轮数: {len(epochs)}
收敛轮数: {convergence_epoch}

硬件配置: {profile.upper()}
模型类型: {self.model_type.title()}
"""
        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"{self.model_type}_training_curves_{profile}.png"
        save_path = save_plot(filename, fig)
        plt.close()
        
        return save_path
    
    def generate_markdown_report(self, train_history: Dict, config: Dict, 
                                optimization_config: Dict, best_accuracy: float,
                                model_path: Path, profile: str, **kwargs) -> Path:
        """
        生成Markdown格式的详细报告
        
        Args:
            train_history: 训练历史数据
            config: 训练配置
            optimization_config: 硬件优化配置
            best_accuracy: 最佳准确率
            model_path: 模型保存路径
            profile: 硬件配置文件
            **kwargs: 其他参数
            
        Returns:
            报告保存路径
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 基础报告内容
        # 模型文件大小安全检查
        model_size_mb = model_path.stat().st_size / (1024*1024) if model_path.exists() else 0.0
        report_content = f"""# {self.model_type.title()}模型训练报告

## 实验信息
- **实验时间**: {timestamp}
- **硬件配置**: {profile.upper()}
- **模型类型**: {self.model_type.title()}模型
- **数据集**: Breakfast Actions (cereals)

## 训练配置
- **批处理大小**: {config['training']['batch_size']}
- **学习率**: {config['training']['learning_rate']:.6f}
- **权重衰减**: {config['training']['weight_decay']:.6f}
- **训练轮数**: {config['training']['num_epochs']}

## 硬件优化配置
- **平台**: {optimization_config.get('platform', 'Unknown')}
- **设备**: {optimization_config.get('device_type', 'Unknown')}
- **混合精度训练**: {'启用' if optimization_config.get('enable_amp', False) else '禁用'}
- **显存使用比例**: {optimization_config.get('memory_fraction', 0.9)*100:.0f}%
- **数据加载器Worker数**: {optimization_config.get('num_workers', 4)}
- **TF32加速**: {'启用' if optimization_config.get('enable_tf32', False) else '禁用'}

## 训练结果
- **最佳测试准确率**: {best_accuracy:.4f}
- **最终训练损失**: {train_history['train_loss'][-1]:.4f}
- **最终训练准确率**: {train_history['train_accuracy'][-1]:.4f}
- **最终测试准确率**: {train_history['test_accuracy'][-1]:.4f}

## 性能分析
- **收敛轮数**: {train_history['epoch'][train_history['test_accuracy'].index(max(train_history['test_accuracy']))] if train_history['test_accuracy'] else 'N/A'}
- **训练稳定性**: {'良好' if len(train_history['train_loss']) > 1 and train_history['train_loss'][-1] < train_history['train_loss'][0] else '需要调优'}
- **过拟合检测**: {'可能存在' if len(train_history['test_accuracy']) > 1 and max(train_history['test_accuracy']) - train_history['test_accuracy'][-1] > 0.05 else '无明显过拟合'}

## 模型信息
- **模型保存路径**: {model_path}
- **模型大小**: {model_size_mb:.2f} MB

## 建议
"""
        
        # 添加基于结果的建议
        suggestions = self._generate_suggestions(best_accuracy, train_history, profile, config)
        for suggestion in suggestions:
            report_content += f"- {suggestion}\n"
        
        # 添加差分模型特定内容
        if self.model_type == "differential":
            report_content += self._add_differential_content(kwargs)
        
        report_content += f"""
## 训练历史数据
```json
{json.dumps(train_history, indent=2, ensure_ascii=False)}
```

---
*报告生成时间: {timestamp}*
"""
        
        # 保存报告
        report_filename = f"{self.model_type}_training_report_{profile}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        report_path = REPORT_DIR / report_filename
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return report_path
    
    def _generate_suggestions(self, best_accuracy: float, train_history: Dict, 
                            profile: str, config: Dict) -> List[str]:
        """生成基于结果的建议"""
        suggestions = []
        
        if best_accuracy < 0.7:
            suggestions.append("准确率较低，建议增加训练轮数或调整学习率")
        
        if len(train_history['train_loss']) > 1 and train_history['train_loss'][-1] > train_history['train_loss'][0]:
            suggestions.append("训练损失未收敛，建议降低学习率或增加训练轮数")
        
        if profile == "a6000" and config['training']['batch_size'] < 96:
            suggestions.append("A6000显存充足，建议使用更大的批处理大小以提升训练效率")
        
        if not suggestions:
            suggestions.append("训练效果良好，可以考虑进一步优化超参数")
        
        return suggestions
    
    def _add_differential_content(self, kwargs: Dict) -> str:
        """添加差分模型特定内容"""
        diff_lr_scale = kwargs.get('diff_lr_scale', 1.0)
        static_model_path = kwargs.get('static_model_path', 'Unknown')
        
        return f"""
## 差分模型特定信息
- **静态模型路径**: {static_model_path}
- **差分学习率缩放**: {diff_lr_scale}x
- **架构特点**: 基于静态模型的差分动态任务图

### 差分组件
1. **差分特征计算器**: 计算执行偏差
2. **权重调整MLP**: 生成图权重调整信号
3. **动态图管理器**: 动态更新任务图权重
4. **融合网络**: 结合静态和动态预测
"""
    
    def save_training_history(self, train_history: Dict, config: Dict,
                            optimization_config: Dict, profile: str, **kwargs) -> Path:
        """
        保存训练历史数据为JSON格式
        
        Args:
            train_history: 训练历史数据
            config: 训练配置
            optimization_config: 硬件优化配置
            profile: 硬件配置文件
            **kwargs: 其他参数
            
        Returns:
            JSON文件保存路径
        """
        history_data = {
            'model_type': self.model_type,
            'train_history': train_history,
            'config': config,
            'optimization_config': optimization_config,
            'profile': profile,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        history_filename = f"{self.model_type}_training_history_{profile}.json"
        history_path = REPORT_DIR / history_filename
        
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False)
        
        return history_path


class InferenceReportGenerator:
    """推理报告生成器"""
    
    def __init__(self):
        """初始化推理报告生成器"""
        REPORT_DIR.mkdir(parents=True, exist_ok=True)
        PICTURE_DIR.mkdir(parents=True, exist_ok=True)
    
    def generate_comparison_report(self, comparison_results: Dict, 
                                 static_results: Dict, diff_results: Dict,
                                 quality_results: Optional[Dict] = None) -> Path:
        """
        生成模型对比报告
        
        Args:
            comparison_results: 完整对比结果
            static_results: 静态模型结果
            diff_results: 差分模型结果
            quality_results: 预测质量结果
            
        Returns:
            报告保存路径
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 计算性能指标
        time_improvement = ((static_results["avg_inference_time"] - diff_results["avg_inference_time"]) 
                           / static_results["avg_inference_time"] * 100)
        acc_improvement = quality_results["improvement_ratio"] if quality_results else 0
        
        report_content = f"""# 静态vs差分模型性能对比报告

## 实验信息
- **测试时间**: {timestamp}
- **计算设备**: {comparison_results.get('device', 'Unknown')}
- **测试类型**: 静态模型 vs 差分动态任务图模型

## 性能对比结果

### 推理时间分析
- **静态模型平均推理时间**: {static_results['avg_inference_time']:.2f} ms
- **差分模型平均推理时间**: {diff_results['avg_inference_time']:.2f} ms
- **时间差异**: {time_improvement:+.2f}% ({'更快' if time_improvement > 0 else '更慢'})

### 预测准确率分析
- **静态模型平均准确率**: {static_results['avg_accuracy']:.4f}
- **差分模型平均准确率**: {diff_results['avg_accuracy']:.4f}
- **准确率提升**: {acc_improvement:+.2f}%

## 结论与建议
"""
        
        # 添加基于结果的建议
        if acc_improvement > 5:
            report_content += "- 差分模型在准确率上有显著提升\n"
        elif acc_improvement > 0:
            report_content += "- 差分模型在准确率上有轻微提升\n"
        else:
            report_content += "- 差分模型准确率略有下降，建议调整参数\n"
        
        if time_improvement > 0:
            report_content += "- 差分模型推理速度更快\n"
        else:
            report_content += "- 静态模型推理速度更快\n"
        
        report_content += f"""
---
*报告生成时间: {timestamp}*
"""
        
        # 保存报告
        report_filename = f"model_comparison_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        report_path = REPORT_DIR / report_filename
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return report_path
