"""
动作原型特征学习模块
通过对比学习优化每个动作类别的代表性特征向量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict


class PrototypeLearner(nn.Module):
    """动作原型特征学习器"""

    def __init__(
        self,
        feature_dim: int,
        prototype_dim: int = 256,
        num_actions: int = 48,
        num_prototypes_per_action: int = 3,
        temperature: float = 0.1,
        momentum: float = 0.999,
    ):
        """
        Args:
            feature_dim: 输入特征维度
            prototype_dim: 原型特征维度
            num_actions: 动作类别数
            num_prototypes_per_action: 每个动作的原型数量
            temperature: 对比学习温度参数
            momentum: 动量更新参数
        """
        super().__init__()

        self.feature_dim = feature_dim
        self.prototype_dim = prototype_dim
        self.num_actions = num_actions
        self.num_prototypes_per_action = num_prototypes_per_action
        self.temperature = temperature
        self.momentum = momentum

        # 特征投影网络
        self.feature_projector = nn.Sequential(
            nn.Linear(feature_dim, prototype_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(prototype_dim * 2, prototype_dim),
            nn.LayerNorm(prototype_dim),
        )

        # 原型特征存储
        total_prototypes = num_actions * num_prototypes_per_action
        self.prototypes = nn.Parameter(
            torch.randn(total_prototypes, prototype_dim), requires_grad=False
        )
        self.register_buffer("prototype_counts", torch.zeros(total_prototypes))

        # 原型到动作的映射
        self.prototype_to_action = {}
        self.action_to_prototypes = {}
        for action_id in range(num_actions):
            start_idx = action_id * num_prototypes_per_action
            end_idx = start_idx + num_prototypes_per_action
            prototype_indices = list(range(start_idx, end_idx))

            self.action_to_prototypes[action_id] = prototype_indices
            for proto_idx in prototype_indices:
                self.prototype_to_action[proto_idx] = action_id

        # 初始化原型
        self._initialize_prototypes()

    def _initialize_prototypes(self):
        """初始化原型特征"""
        nn.init.xavier_uniform_(self.prototypes)
        # 归一化
        self.prototypes.data = F.normalize(self.prototypes.data, dim=1)

    def forward(
        self,
        features: torch.Tensor,
        labels: torch.Tensor,
        update_prototypes: bool = True,
    ) -> Dict:
        """
        Args:
            features: [batch_size, seq_len, feature_dim] 输入特征
            labels: [batch_size, seq_len] 动作标签
            update_prototypes: 是否更新原型

        Returns:
            包含原型学习结果的字典
        """
        batch_size, seq_len, _ = features.shape

        # 投影特征
        projected_features = self.feature_projector(
            features.reshape(-1, self.feature_dim)
        )
        projected_features = F.normalize(projected_features, dim=1)
        projected_features = projected_features.reshape(
            batch_size, seq_len, self.prototype_dim
        )

        # 计算与原型的相似度
        similarities = self._compute_similarities(projected_features)

        # 原型分配
        prototype_assignments = self._assign_prototypes(similarities, labels)

        # 更新原型由训练脚本在反向传播后显式调用

        # 计算原型质量指标
        prototype_quality = self._compute_prototype_quality(projected_features, labels)

        return {
            "projected_features": projected_features,
            "similarities": similarities,
            "prototype_assignments": prototype_assignments,
            "prototype_quality": prototype_quality,
            "prototypes": self.prototypes.clone(),
        }

    def _compute_similarities(self, features: torch.Tensor) -> torch.Tensor:
        """计算特征与所有原型的相似度"""
        batch_size, seq_len, _ = features.shape

        # 重塑特征: [batch_size * seq_len, prototype_dim]
        flat_features = features.view(-1, self.prototype_dim)

        # 计算余弦相似度: [batch_size * seq_len, num_prototypes]
        similarities = torch.mm(flat_features, self.prototypes.t())

        # 重塑回原始形状: [batch_size, seq_len, num_prototypes]
        similarities = similarities.view(batch_size, seq_len, -1)

        return similarities

    def _assign_prototypes(
        self, similarities: torch.Tensor, labels: torch.Tensor
    ) -> torch.Tensor:
        """为每个特征分配最佳原型 - 修复torch.compile兼容性"""
        batch_size, seq_len, _ = similarities.shape
        assignments = torch.zeros_like(labels, dtype=torch.long)

        # 修复torch.compile兼容性：避免在编译模式下使用.item()和字典查找
        # 使用张量操作替代循环和字典查找
        device = similarities.device

        # 创建动作到原型的映射张量
        max_action = max(self.action_to_prototypes.keys()) if self.action_to_prototypes else 0
        action_prototype_map = torch.full((max_action + 1, self.num_prototypes_per_action),
                                        -1, dtype=torch.long, device=device)

        for action_id, prototype_indices in self.action_to_prototypes.items():
            for i, proto_idx in enumerate(prototype_indices):
                if i < self.num_prototypes_per_action:
                    action_prototype_map[action_id, i] = proto_idx

        # 使用张量操作进行批量分配
        valid_mask = (labels >= 0) & (labels <= max_action)

        for b in range(batch_size):
            for t in range(seq_len):
                if valid_mask[b, t]:
                    label = labels[b, t]
                    # 获取该动作的候选原型
                    candidate_prototypes = action_prototype_map[label]
                    valid_candidates = candidate_prototypes[candidate_prototypes >= 0]

                    if len(valid_candidates) > 0:
                        # 在候选原型中选择相似度最高的
                        candidate_sims = similarities[b, t, valid_candidates]
                        best_candidate_idx = torch.argmax(candidate_sims)
                        assignments[b, t] = valid_candidates[best_candidate_idx]

        return assignments

    def _update_prototypes(self, features: torch.Tensor, labels: torch.Tensor):
        """使用动量更新原型特征 - 修复torch.compile兼容性"""
        batch_size, seq_len, _ = features.shape
        device = features.device

        # 修复torch.compile兼容性：使用张量操作替代字典和.item()
        max_action = max(self.action_to_prototypes.keys()) if self.action_to_prototypes else 0

        # 创建动作到原型的映射张量
        action_prototype_map = torch.full((max_action + 1, self.num_prototypes_per_action),
                                        -1, dtype=torch.long, device=device)

        for action_id, prototype_indices in self.action_to_prototypes.items():
            for i, proto_idx in enumerate(prototype_indices):
                if i < self.num_prototypes_per_action:
                    action_prototype_map[action_id, i] = proto_idx

        # 收集每个原型的特征更新
        prototype_feature_sums = torch.zeros_like(self.prototypes)
        prototype_update_counts = torch.zeros(self.prototypes.shape[0], device=device)

        valid_mask = (labels >= 0) & (labels <= max_action)

        for b in range(batch_size):
            for t in range(seq_len):
                if valid_mask[b, t]:
                    label = labels[b, t]
                    feature = features[b, t]

                    # 获取候选原型
                    candidate_prototypes = action_prototype_map[label]
                    valid_candidates = candidate_prototypes[candidate_prototypes >= 0]

                    if len(valid_candidates) > 0:
                        # 找到最相似的原型
                        similarities = torch.mm(
                            feature.unsqueeze(0), self.prototypes[valid_candidates].t()
                        )
                        best_candidate_idx = torch.argmax(similarities)
                        best_proto_idx = valid_candidates[best_candidate_idx]

                        # 累积特征更新
                        prototype_feature_sums[best_proto_idx] += feature
                        prototype_update_counts[best_proto_idx] += 1

        # 应用动量更新
        with torch.no_grad():
            for proto_idx in range(self.prototypes.shape[0]):
                if prototype_update_counts[proto_idx] > 0:
                    avg_feature = prototype_feature_sums[proto_idx] / prototype_update_counts[proto_idx]
                    avg_feature = F.normalize(avg_feature, dim=0)

                    self.prototypes[proto_idx] = (
                        self.momentum * self.prototypes[proto_idx]
                        + (1 - self.momentum) * avg_feature
                    )
                    self.prototypes[proto_idx] = F.normalize(
                        self.prototypes[proto_idx], dim=0
                    )
                    self.prototype_counts[proto_idx] += prototype_update_counts[proto_idx]

    def update_prototypes(self, features: torch.Tensor, labels: torch.Tensor):
        """Public method to update prototypes after backprop."""
        self._update_prototypes(features, labels)

    def _compute_prototype_quality(
        self, features: torch.Tensor, labels: torch.Tensor
    ) -> Dict[str, float]:
        """
        计算原型质量指标 - torch.compile兼容版本

        注意：此函数已重构为纯PyTorch操作，兼容torch.compile
        移除了所有.tolist()调用和numpy操作
        """
        batch_size, seq_len, _ = features.shape
        device = features.device

        # 获取有效的动作ID范围
        max_action = max(self.action_to_prototypes.keys()) if self.action_to_prototypes else 0
        valid_mask = (labels >= 0) & (labels <= max_action)

        if not valid_mask.any():
            # 返回默认值，避免空张量操作
            return {
                "intra_class_distance": 0.0,
                "inter_class_distance": 0.0,
                "separation_ratio": 1.0
            }

        # 展平有效的特征和标签
        valid_features = features[valid_mask]  # [num_valid, feature_dim]
        valid_labels = labels[valid_mask]      # [num_valid]

        # 计算类内距离（特征与对应原型的距离）
        intra_distances = []

        for action_id in range(max_action + 1):
            if action_id in self.action_to_prototypes:
                # 获取该动作的特征
                action_mask = (valid_labels == action_id)
                if action_mask.sum() > 0:
                    action_features = valid_features[action_mask]
                    action_prototypes = self.prototypes[self.action_to_prototypes[action_id]]

                    # 计算特征与原型的相似度
                    similarities = torch.mm(action_features, action_prototypes.t())
                    max_sims, _ = torch.max(similarities, dim=1)
                    distances = 1 - max_sims  # 距离 = 1 - 相似度
                    intra_distances.append(distances)

        # 计算类间距离（不同动作原型间的距离）
        inter_distances = []

        for i in range(self.num_actions):
            for j in range(i + 1, self.num_actions):
                if i in self.action_to_prototypes and j in self.action_to_prototypes:
                    prototypes_i = self.prototypes[self.action_to_prototypes[i]]
                    prototypes_j = self.prototypes[self.action_to_prototypes[j]]

                    # 计算两组原型间的最大相似度
                    similarities = torch.mm(prototypes_i, prototypes_j.t())
                    max_sim = torch.max(similarities)
                    distance = 1 - max_sim
                    inter_distances.append(distance)

        # 【修复】使用纯PyTorch操作计算平均值，加强空列表和空张量检查
        quality_metrics = {}

        if intra_distances:
            # 过滤掉空张量，避免torch.cat失败
            valid_intra_distances = [d for d in intra_distances if d.numel() > 0]
            if valid_intra_distances:
                intra_tensor = torch.cat(valid_intra_distances)
                if intra_tensor.numel() > 0:
                    quality_metrics["intra_class_distance"] = torch.mean(intra_tensor).item()
                else:
                    quality_metrics["intra_class_distance"] = 0.0
            else:
                quality_metrics["intra_class_distance"] = 0.0
        else:
            quality_metrics["intra_class_distance"] = 0.0

        if inter_distances:
            # 过滤掉无效的距离值（NaN或inf）
            valid_inter_distances = [d for d in inter_distances if torch.isfinite(d)]
            if valid_inter_distances:
                inter_tensor = torch.stack(valid_inter_distances)
                if inter_tensor.numel() > 0:
                    quality_metrics["inter_class_distance"] = torch.mean(inter_tensor).item()
                else:
                    quality_metrics["inter_class_distance"] = 0.0
            else:
                quality_metrics["inter_class_distance"] = 0.0
        else:
            quality_metrics["inter_class_distance"] = 0.0

        # 分离度指标：类间距离 / 类内距离
        if quality_metrics["intra_class_distance"] > 0:
            quality_metrics["separation_ratio"] = (
                quality_metrics["inter_class_distance"] / quality_metrics["intra_class_distance"]
            )
        else:
            quality_metrics["separation_ratio"] = 1.0

        return quality_metrics

    def get_action_prototypes(self, action_id: int) -> torch.Tensor:
        """获取指定动作的原型特征"""
        if action_id in self.action_to_prototypes:
            prototype_indices = self.action_to_prototypes[action_id]
            return self.prototypes[prototype_indices]
        else:
            return torch.empty(0, self.prototype_dim)

    def compute_prototype_centers(self) -> torch.Tensor:
        """计算每个动作的原型中心"""
        # 在与原型相同的设备上创建结果张量，确保设备一致性
        centers = torch.zeros(
            self.num_actions, self.prototype_dim, device=self.prototypes.device
        )

        for action_id in range(self.num_actions):
            if action_id in self.action_to_prototypes:
                action_prototypes = self.get_action_prototypes(action_id)
                centers[action_id] = action_prototypes.mean(dim=0)

        return centers


class PrototypeLoss(nn.Module):
    """原型学习的对比损失函数"""

    def __init__(self, temperature: float = 0.1, margin: float = 0.5):
        super().__init__()
        self.temperature = temperature
        self.margin = margin

    def forward(
        self,
        similarities: torch.Tensor,
        labels: torch.Tensor,
        prototype_to_action_map: torch.Tensor,
    ) -> torch.Tensor:
        """
        计算对比学习损失 (InfoNCE)

        Args:
            similarities: [batch_size, seq_len, num_prototypes] 相似度矩阵
            labels: [batch_size, seq_len] 真实标签 (>=0 有效)
            prototype_to_action_map: [num_prototypes] 原型到动作映射张量

        Returns:
            单值损失张量
        """
        device = similarities.device
        batch_size, seq_len, num_prototypes = similarities.shape

        # 展平到样本维度
        sims_flat = similarities.view(-1, num_prototypes)
        labels_flat = labels.view(-1)
        valid_mask = labels_flat >= 0
        if not valid_mask.any():
            return torch.tensor(0.0, device=device)

        sims_flat = sims_flat[valid_mask]
        labels_flat = labels_flat[valid_mask]

        # 正负样本掩码
        pos_mask = prototype_to_action_map.unsqueeze(0) == labels_flat.unsqueeze(1)
        neg_mask = ~pos_mask
        valid_samples = pos_mask.any(dim=1) & neg_mask.any(dim=1)
        if not valid_samples.any():
            return torch.tensor(0.0, device=device)

        sims_valid = sims_flat[valid_samples]
        pos_mask = pos_mask[valid_samples]

        # 向量化InfoNCE计算
        pos_sims = sims_valid.masked_fill(~pos_mask, float('-inf'))
        max_pos, _ = pos_sims.max(dim=1)

        lse = torch.logsumexp(sims_valid / self.temperature, dim=1)
        losses = lse - (max_pos / self.temperature)

        loss = losses.mean()
        return loss
