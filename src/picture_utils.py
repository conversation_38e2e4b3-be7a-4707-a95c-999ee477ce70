"""
图片保存工具模块
提供统一的图片保存功能，自动创建picture文件夹
"""

import os
import matplotlib.pyplot as plt
from typing import Optional


def get_picture_dir() -> str:
    """获取picture目录路径，可通过环境变量 ``PICTURE_DIR`` 覆盖默认值"""
    picture_dir = os.environ.get(
        "PICTURE_DIR", "/data2/syd_data/Breakfast_Data/Outputs/pictures"
    )

    # 确保目录存在
    os.makedirs(picture_dir, exist_ok=True)

    return picture_dir


def save_figure(filename: str,
                fig: Optional[plt.Figure] = None,
                dpi: int = 300,
                bbox_inches: str = "tight") -> str:
    """
    保存图片到统一的picture目录 - Linux专用

    Args:
        filename: 文件名（包含扩展名）
        fig: matplotlib图形对象，如果为None则使用当前图形
        dpi: 图片分辨率
        bbox_inches: 边界框设置

    Returns:
        保存的文件路径
    """
    # 获取picture目录
    picture_dir = get_picture_dir()
    picture_path = os.path.join(picture_dir, filename)

    # 保存到picture目录
    if fig is not None:
        fig.savefig(picture_path, dpi=dpi, bbox_inches=bbox_inches)
    else:
        plt.savefig(picture_path, dpi=dpi, bbox_inches=bbox_inches)

    print(f"✅ 图片已保存到 {picture_path}")
    return picture_path


def save_current_figure(filename: str, **kwargs) -> str:
    """
    保存当前matplotlib图形
    
    Args:
        filename: 文件名
        **kwargs: 传递给save_figure的其他参数
        
    Returns:
        保存的文件路径
    """
    return save_figure(filename, fig=None, **kwargs)


def create_picture_subdirectory(subdir_name: str) -> str:
    """
    在picture目录下创建子目录
    
    Args:
        subdir_name: 子目录名称
        
    Returns:
        子目录的完整路径
    """
    picture_dir = get_picture_dir()
    subdir_path = os.path.join(picture_dir, subdir_name)
    os.makedirs(subdir_path, exist_ok=True)
    return subdir_path


def get_picture_path(filename: str, subdir: Optional[str] = None) -> str:
    """
    获取picture目录中文件的完整路径
    
    Args:
        filename: 文件名
        subdir: 可选的子目录名
        
    Returns:
        文件的完整路径
    """
    if subdir:
        base_dir = create_picture_subdirectory(subdir)
    else:
        base_dir = get_picture_dir()
    
    return os.path.join(base_dir, filename)


def list_saved_pictures(subdir: Optional[str] = None) -> list:
    """
    列出已保存的图片文件
    
    Args:
        subdir: 可选的子目录名
        
    Returns:
        图片文件列表
    """
    if subdir:
        search_dir = os.path.join(get_picture_dir(), subdir)
    else:
        search_dir = get_picture_dir()
    
    if not os.path.exists(search_dir):
        return []
    
    # 常见图片扩展名
    image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.pdf'}
    
    pictures = []
    for filename in os.listdir(search_dir):
        if any(filename.lower().endswith(ext) for ext in image_extensions):
            pictures.append(os.path.join(search_dir, filename))
    
    return sorted(pictures)


if __name__ == "__main__":
    # 测试功能
    import matplotlib.pyplot as plt
    import numpy as np
    
    # 创建测试图片
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    plt.figure(figsize=(8, 6))
    plt.plot(x, y, 'b-', linewidth=2)
    plt.title('Test Plot')
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.grid(True, alpha=0.3)
    
    # 保存图片
    saved_path = save_current_figure("test_plot.png")
    plt.close()
    
    print(f"测试图片已保存到: {saved_path}")
    
    # 列出已保存的图片
    pictures = list_saved_pictures()
    print(f"已保存的图片: {pictures}")
