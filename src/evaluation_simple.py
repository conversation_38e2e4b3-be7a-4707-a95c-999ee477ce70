"""
简化的评估模块
提供基础的定量评估指标
"""

import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, precision_score, recall_score
from typing import Dict, List, Optional
from pathlib import Path


class Evaluator:
    """简化的评估器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.num_actions = config['data']['num_actions']
        
    def evaluate_predictions(self, 
                           predictions: np.ndarray, 
                           labels: np.ndarray) -> Dict[str, float]:
        """评估动作识别预测结果"""
        # 过滤有效标签（忽略填充标签-1）
        valid_mask = labels >= 0
        valid_predictions = predictions[valid_mask]
        valid_labels = labels[valid_mask]
        
        if len(valid_labels) == 0:
            return {}
        
        metrics = {}
        
        # 帧级别准确率
        metrics['frame_accuracy'] = accuracy_score(valid_labels, valid_predictions)
        
        # F1分数
        metrics['f1_score'] = f1_score(valid_labels, valid_predictions, average='weighted', zero_division=0)
        metrics['f1_macro'] = f1_score(valid_labels, valid_predictions, average='macro', zero_division=0)
        metrics['f1_micro'] = f1_score(valid_labels, valid_predictions, average='micro', zero_division=0)
        
        # 精确率和召回率
        metrics['precision'] = precision_score(valid_labels, valid_predictions, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(valid_labels, valid_predictions, average='weighted', zero_division=0)
        
        # 段级别准确率
        segment_accuracy = self._compute_segment_accuracy(predictions, labels)
        metrics['segment_accuracy'] = segment_accuracy

        # 多IoU阈值下的mAP评估
        map_metrics = self._compute_map_at_multiple_ious(predictions, labels)
        metrics.update(map_metrics)
        
        # 编辑距离
        edit_distance = self._compute_edit_distance(predictions, labels)
        metrics['edit_distance'] = edit_distance
        
        return metrics
    
    def _compute_segment_accuracy(self, predictions: np.ndarray, labels: np.ndarray,
                                 iou_threshold: float = 0.5, use_hungarian: bool = False) -> float:
        """
        计算段级别准确率，支持多种匹配策略

        Args:
            predictions: 预测序列
            labels: 真实标签序列
            iou_threshold: IoU阈值，默认0.5
            use_hungarian: 是否使用匈牙利算法进行最优匹配，默认False

        Returns:
            段级别准确率

        Note:
            - 默认使用贪心匹配策略（与原实现兼容）
            - 可选择匈牙利算法解决多对一/一对多匹配问题
        """
        if len(predictions.shape) == 1:
            predictions = predictions.reshape(1, -1)
            labels = labels.reshape(1, -1)

        correct_segments = 0
        total_segments = 0

        for i in range(predictions.shape[0]):
            pred_seq = predictions[i]
            label_seq = labels[i]

            # 提取段
            pred_segments = self._extract_segments(pred_seq)
            label_segments = self._extract_segments(label_seq)

            if use_hungarian:
                # 使用匈牙利算法进行最优匹配
                matched_pairs = self._hungarian_segment_matching(
                    pred_segments, label_segments, iou_threshold
                )

                # 统计匹配结果
                for label_seg in label_segments:
                    if label_seg['action'] >= 0:  # 忽略填充
                        total_segments += 1
                        # 检查是否在匹配对中
                        for pred_idx, label_idx in matched_pairs:
                            if label_segments[label_idx] == label_seg:
                                correct_segments += 1
                                break
            else:
                # 使用原始贪心匹配策略（保持向后兼容）
                used_pred_segments = set()  # 防止一个预测段匹配多个真实段

                for label_seg in label_segments:
                    if label_seg['action'] >= 0:  # 忽略填充
                        total_segments += 1
                        # 检查是否有匹配的预测段
                        for j, pred_seg in enumerate(pred_segments):
                            if (j not in used_pred_segments and
                                pred_seg['action'] == label_seg['action'] and
                                self._segments_overlap(pred_seg, label_seg, iou_threshold)):
                                correct_segments += 1
                                used_pred_segments.add(j)  # 标记已使用
                                break

        return correct_segments / total_segments if total_segments > 0 else 0.0
    
    def _extract_segments(self, sequence: np.ndarray) -> List[Dict]:
        """从序列中提取段"""
        segments = []
        if len(sequence) == 0:
            return segments
        
        current_action = sequence[0]
        start_frame = 0
        
        for i in range(1, len(sequence)):
            if sequence[i] != current_action:
                segments.append({
                    'action': current_action,
                    'start': start_frame,
                    'end': i - 1
                })
                current_action = sequence[i]
                start_frame = i
        
        # 添加最后一个段
        segments.append({
            'action': current_action,
            'start': start_frame,
            'end': len(sequence) - 1
        })
        
        return segments
    
    def _hungarian_segment_matching(self, pred_segments: List[Dict],
                                   label_segments: List[Dict],
                                   iou_threshold: float = 0.5) -> List[tuple]:
        """
        使用匈牙利算法进行段匹配，解决多对一/一对多匹配问题

        Args:
            pred_segments: 预测段列表
            label_segments: 真实段列表
            iou_threshold: IoU阈值

        Returns:
            匹配对列表 [(pred_idx, label_idx), ...]
        """
        try:
            from scipy.optimize import linear_sum_assignment
        except ImportError:
            print("⚠️ scipy未安装，回退到贪心匹配策略")
            return []

        if not pred_segments or not label_segments:
            return []

        # 构建成本矩阵（使用负IoU作为成本，因为匈牙利算法求最小成本）
        cost_matrix = np.full((len(pred_segments), len(label_segments)), 1.0)

        for i, pred_seg in enumerate(pred_segments):
            for j, label_seg in enumerate(label_segments):
                if (pred_seg['action'] == label_seg['action'] and
                    label_seg['action'] >= 0):  # 忽略填充标签
                    iou = self._compute_segment_iou(pred_seg, label_seg)
                    if iou >= iou_threshold:
                        cost_matrix[i, j] = 1.0 - iou  # 转换为成本

        # 执行匈牙利算法
        pred_indices, label_indices = linear_sum_assignment(cost_matrix)

        # 过滤有效匹配（成本小于1.0 - iou_threshold）
        valid_matches = []
        for pred_idx, label_idx in zip(pred_indices, label_indices):
            if cost_matrix[pred_idx, label_idx] < (1.0 - iou_threshold):
                valid_matches.append((pred_idx, label_idx))

        return valid_matches

    def _compute_segment_iou(self, seg1: Dict, seg2: Dict) -> float:
        """
        计算两个段的IoU值

        Args:
            seg1: 第一个段
            seg2: 第二个段

        Returns:
            IoU值 (0.0 - 1.0)
        """
        overlap_start = max(seg1['start'], seg2['start'])
        overlap_end = min(seg1['end'], seg2['end'])

        if overlap_start > overlap_end:
            return 0.0

        overlap_length = overlap_end - overlap_start + 1
        seg1_length = seg1['end'] - seg1['start'] + 1
        seg2_length = seg2['end'] - seg2['start'] + 1

        # 计算标准IoU (Intersection over Union)
        union_length = seg1_length + seg2_length - overlap_length
        iou = overlap_length / union_length if union_length > 0 else 0.0

        return iou

    def _segments_overlap(self, seg1: Dict, seg2: Dict, threshold: float = 0.5) -> bool:
        """
        检查两个段是否重叠，使用标准IoU (Intersection over Union) 指标

        Args:
            seg1: 第一个段，包含 'start' 和 'end' 键
            seg2: 第二个段，包含 'start' 和 'end' 键
            threshold: IoU阈值，默认0.5

        Returns:
            bool: 如果IoU >= threshold则返回True，否则返回False

        Note:
            使用标准IoU公式: overlap_length / (seg1_length + seg2_length - overlap_length)
            这与学术界标准一致，便于与其他研究成果进行公平比较
        """
        iou = self._compute_segment_iou(seg1, seg2)
        return iou >= threshold

    def _compute_map_at_multiple_ious(self, predictions: np.ndarray, labels: np.ndarray) -> Dict[str, float]:
        """
        计算多个IoU阈值下的mAP (mean Average Precision)

        Args:
            predictions: 预测序列
            labels: 真实标签序列

        Returns:
            包含不同IoU阈值下mAP的字典
        """
        iou_thresholds = [0.1, 0.25, 0.5, 0.75, 0.9]
        map_metrics = {}

        # 计算每个IoU阈值下的段级别准确率
        for iou_thresh in iou_thresholds:
            # 使用贪心匹配
            greedy_acc = self._compute_segment_accuracy(predictions, labels, iou_thresh, use_hungarian=False)
            map_metrics[f'segment_acc@{iou_thresh}'] = greedy_acc

            # 使用匈牙利算法匹配（如果scipy可用）
            try:
                hungarian_acc = self._compute_segment_accuracy(predictions, labels, iou_thresh, use_hungarian=True)
                map_metrics[f'segment_acc_hungarian@{iou_thresh}'] = hungarian_acc
            except:
                pass  # scipy不可用时跳过

        # 计算平均mAP
        greedy_aps = [map_metrics[f'segment_acc@{thresh}'] for thresh in iou_thresholds]
        map_metrics['mAP_greedy'] = np.mean(greedy_aps)

        # 匈牙利算法的mAP（如果可用）
        hungarian_keys = [k for k in map_metrics.keys() if 'hungarian' in k]
        if hungarian_keys:
            hungarian_aps = [map_metrics[k] for k in hungarian_keys]
            map_metrics['mAP_hungarian'] = np.mean(hungarian_aps)

        return map_metrics
    
    def _compute_edit_distance(self, predictions: np.ndarray, labels: np.ndarray) -> float:
        """计算编辑距离（Levenshtein距离）"""
        if len(predictions.shape) == 1:
            predictions = predictions.reshape(1, -1)
            labels = labels.reshape(1, -1)
        
        total_distance = 0
        total_length = 0
        
        for i in range(predictions.shape[0]):
            pred_seq = predictions[i]
            label_seq = labels[i]
            
            # 过滤有效标签
            valid_mask = label_seq >= 0
            pred_seq = pred_seq[valid_mask]
            label_seq = label_seq[valid_mask]
            
            if len(label_seq) > 0:
                distance = self._levenshtein_distance(pred_seq, label_seq)
                total_distance += distance
                total_length += len(label_seq)
        
        return total_distance / total_length if total_length > 0 else 0.0
    
    def _levenshtein_distance(self, seq1: np.ndarray, seq2: np.ndarray) -> int:
        """计算两个序列的Levenshtein距离"""
        m, n = len(seq1), len(seq2)
        
        # 创建距离矩阵
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        # 初始化
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        # 填充矩阵
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if seq1[i-1] == seq2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
        
        return dp[m][n]
    
    def evaluate_task_graph(self, 
                          predicted_graph: Dict, 
                          ground_truth_graph: Optional[Dict] = None) -> Dict[str, float]:
        """评估任务图质量"""
        metrics = {}
        
        # 图结构指标
        if 'adjacency_matrices' in predicted_graph:
            adj_matrices = predicted_graph['adjacency_matrices']
            if len(adj_matrices.shape) == 3:
                # 取第一个样本的邻接矩阵
                adjacency_matrix = adj_matrices[0].cpu().numpy()
            else:
                adjacency_matrix = adj_matrices.cpu().numpy()
        else:
            adjacency_matrix = predicted_graph.get('adjacency_matrix', torch.zeros(self.num_actions, self.num_actions)).cpu().numpy()
        
        # 转换为NetworkX图
        import networkx as nx
        G = nx.from_numpy_array(adjacency_matrix, create_using=nx.DiGraph)
        
        # 连通性
        metrics['is_connected'] = float(nx.is_weakly_connected(G))
        metrics['num_components'] = nx.number_weakly_connected_components(G)
        
        # 是否为DAG
        metrics['is_dag'] = float(nx.is_directed_acyclic_graph(G))
        
        # 密度
        metrics['graph_density'] = nx.density(G)
        
        # 平均路径长度
        if nx.is_weakly_connected(G):
            try:
                metrics['avg_path_length'] = nx.average_shortest_path_length(G.to_undirected())
            except:
                metrics['avg_path_length'] = float('inf')
        else:
            metrics['avg_path_length'] = float('inf')
        
        # 边数量
        metrics['num_edges'] = G.number_of_edges()
        metrics['num_nodes'] = G.number_of_nodes()
        
        return metrics
    
    def evaluate_prototype_quality(self, 
                                 prototype_outputs: Dict,
                                 labels: torch.Tensor) -> Dict[str, float]:
        """评估原型特征质量"""
        metrics = {}
        
        if 'similarities' not in prototype_outputs:
            return metrics
        
        similarities = prototype_outputs['similarities']
        
        # 计算原型分配的准确性
        if len(similarities.shape) == 3:  # [batch_size, seq_len, num_prototypes]
            batch_size, seq_len, num_prototypes = similarities.shape
            
            # 展平
            flat_similarities = similarities.view(-1, num_prototypes)
            flat_labels = labels.view(-1)
            
            # 过滤有效标签
            valid_mask = flat_labels >= 0
            if valid_mask.sum() > 0:
                valid_similarities = flat_similarities[valid_mask]
                valid_labels = flat_labels[valid_mask]
                
                # 计算原型分配准确率
                prototype_assignments = torch.argmax(valid_similarities, dim=1)
                
                # 简单的质量指标：相同动作的原型相似度
                same_action_similarities = []
                diff_action_similarities = []
                
                for i in range(len(valid_labels)):
                    for j in range(i+1, len(valid_labels)):
                        sim = torch.cosine_similarity(
                            valid_similarities[i].unsqueeze(0),
                            valid_similarities[j].unsqueeze(0)
                        ).item()
                        
                        if valid_labels[i] == valid_labels[j]:
                            same_action_similarities.append(sim)
                        else:
                            diff_action_similarities.append(sim)
                
                if same_action_similarities:
                    metrics['intra_class_similarity'] = np.mean(same_action_similarities)
                if diff_action_similarities:
                    metrics['inter_class_similarity'] = np.mean(diff_action_similarities)
                
                # 分离度
                if same_action_similarities and diff_action_similarities:
                    metrics['separation_ratio'] = (
                        np.mean(same_action_similarities) - np.mean(diff_action_similarities)
                    )
        
        return metrics
    
    def evaluate(self, model, test_loader, device) -> Dict[str, float]:
        """
        评估模型在测试集上的性能

        Args:
            model: 训练好的模型
            test_loader: 测试数据加载器
            device: 计算设备

        Returns:
            包含各种评估指标的字典
        """
        model.eval()
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for batch in test_loader:
                # 将数据移动到设备
                for key in ['features', 'labels', 'sequence_length']:
                    if key in batch:
                        batch[key] = batch[key].to(device)

                # 模型推理
                outputs = model(batch, mode='eval')

                # 处理不同的输出格式
                if 'action_predictions' in outputs:
                    predictions = outputs['action_predictions']
                elif 'action_logits' in outputs:
                    # 如果是logits，需要转换为预测类别
                    predictions = torch.argmax(outputs['action_logits'], dim=-1)
                else:
                    raise KeyError("模型输出中未找到 'action_predictions' 或 'action_logits'")

                labels = batch['labels']

                # 转换为numpy数组并收集
                all_predictions.append(predictions.cpu().numpy())
                all_labels.append(labels.cpu().numpy())

        # 合并所有批次的结果
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)

        # 使用现有的evaluate_predictions方法
        return self.evaluate_predictions(all_predictions, all_labels)

    def create_confusion_matrix(self,
                              predictions: np.ndarray,
                              labels: np.ndarray,
                              action_names: Optional[List[str]] = None,
                              save_path: Optional[str] = None):
        """创建混淆矩阵"""
        # 过滤有效标签
        valid_mask = labels >= 0
        valid_predictions = predictions[valid_mask]
        valid_labels = labels[valid_mask]

        if len(valid_labels) == 0:
            return None

        # 计算混淆矩阵
        cm = confusion_matrix(valid_labels, valid_predictions)

        # 创建图表
        plt.figure(figsize=(10, 8))

        # 绘制热力图
        if action_names is None:
            action_names = [f"Action_{i}" for i in range(cm.shape[0])]

        plt.imshow(cm, interpolation='nearest', cmap='Blues')
        plt.colorbar()

        # 添加数值标注
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                plt.text(j, i, str(cm[i, j]), ha='center', va='center')

        plt.title('Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('True')

        # 设置刻度标签
        tick_marks = np.arange(len(action_names))
        plt.xticks(tick_marks, action_names[:cm.shape[1]], rotation=45)
        plt.yticks(tick_marks, action_names[:cm.shape[0]])

        plt.tight_layout()

        if save_path:
            # 使用统一的图片保存工具
            try:
                # 尝试导入picture_utils
                import sys
                sys.path.append(os.path.dirname(__file__))
                from picture_utils import save_figure

                # 如果save_path是相对路径，使用统一保存工具
                if not os.path.isabs(save_path):
                    save_figure(save_path, fig=plt.gcf())
                else:
                    # 如果是绝对路径，直接保存到指定位置
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    print(f"✅ 混淆矩阵已保存到 {save_path}")

            except ImportError:
                # 如果picture_utils不可用，使用传统方式
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 混淆矩阵已保存到 {save_path}")

        return plt.gcf()


if __name__ == "__main__":
    # 测试评估器
    config = {
        'data': {'num_actions': 5}
    }
    
    evaluator = Evaluator(config)
    
    # 创建测试数据
    predictions = np.random.randint(0, 5, (10, 50))
    labels = np.random.randint(0, 5, (10, 50))
    
    # 测试评估
    metrics = evaluator.evaluate_predictions(predictions, labels)
    print("评估指标:", metrics)
