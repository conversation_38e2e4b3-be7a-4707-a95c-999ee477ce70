"""
主模型架构
整合静态任务图学习、动作原型特征和多视角融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict

from src.task_graph_simple import TaskGraphEncoder, TaskGraphLoss
from src.prototype_learning import Prototype<PERSON>earner, PrototypeLoss
from src.multi_view_fusion import MultiViewFusion, MultiViewLoss


class ProceduralVideoUnderstanding(nn.Module):
    """
    程序性视频理解主模型

    架构说明：
    - 时序编码：使用双向LSTM，不使用Transformer架构
    - 多视角融合：使用简化融合方法（concat/mean），不使用注意力机制
    - 任务图学习：使用简化GCN，不使用注意力机制
    - 因此不需要Flash Attention优化
    """

    def __init__(self, config: Dict):
        super().__init__()

        self.config = config

        # 提取配置参数
        data_config = config["data"]
        model_config = config["model"]

        self.feature_dim = data_config["feature_dim"]
        self.num_actions = data_config["num_actions"]
        self.max_seq_length = data_config["max_sequence_length"]

        # 多视角融合模块
        self.multi_view_fusion = MultiViewFusion(
            input_dim=self.feature_dim,
            hidden_dim=model_config["fusion"]["view_encoder_dim"],
            output_dim=model_config["task_graph"]["hidden_dim"],
            num_views=len(data_config["train_views"]),
            fusion_method=model_config["fusion"]["fusion_method"],
            domain_adaptation=model_config["fusion"]["domain_adaptation"],
            uncertainty_weighting=model_config["fusion"]["uncertainty_weighting"],
        )

        # 时序特征编码器
        self.temporal_encoder = nn.LSTM(
            input_size=model_config["task_graph"]["hidden_dim"],
            hidden_size=model_config["task_graph"]["hidden_dim"] // 2,
            num_layers=2,
            batch_first=True,
            bidirectional=True,
            dropout=model_config["task_graph"]["dropout"],
        )

        # 动作分类器
        self.action_classifier = nn.Sequential(
            nn.Linear(
                model_config["task_graph"]["hidden_dim"],
                model_config["task_graph"]["hidden_dim"] // 2,
            ),
            nn.ReLU(),
            nn.Dropout(model_config["task_graph"]["dropout"]),
            nn.Linear(model_config["task_graph"]["hidden_dim"] // 2, self.num_actions),
        )

        # 静态任务图学习模块（简化版本）
        self.task_graph_encoder = TaskGraphEncoder(
            num_actions=self.num_actions,
            hidden_dim=model_config["task_graph"]["hidden_dim"],
            num_gnn_layers=model_config["task_graph"]["num_gnn_layers"],
            dropout=model_config["task_graph"]["dropout"],
            graph_pooling=model_config["task_graph"].get("graph_pooling", "mean"),
        )

        # 动作原型学习模块
        self.prototype_learner = PrototypeLearner(
            feature_dim=model_config["task_graph"]["hidden_dim"],
            prototype_dim=model_config["prototype"]["prototype_dim"],
            num_actions=self.num_actions,
            num_prototypes_per_action=model_config["prototype"][
                "num_prototypes_per_action"
            ],
            temperature=model_config["prototype"]["temperature"],
            momentum=model_config["prototype"]["momentum"],
        )

        # 损失函数
        self.task_graph_loss = TaskGraphLoss(
            structure_weight=config["training"]["loss_weights"]["task_graph_structure"],
            temporal_weight=config["training"]["loss_weights"]["temporal_consistency"],
        )

        self.prototype_loss = PrototypeLoss(
            temperature=model_config["prototype"]["temperature"]
        )

        self.multi_view_loss = MultiViewLoss(
            domain_weight=config["training"]["loss_weights"]["view_alignment"]
        )

        # 时序一致性约束
        self.temporal_consistency_net = nn.Sequential(
            nn.Linear(
                model_config["task_graph"]["hidden_dim"],
                model_config["task_graph"]["hidden_dim"] // 2,
            ),
            nn.ReLU(),
            nn.Linear(
                model_config["task_graph"]["hidden_dim"] // 2,
                model_config["task_graph"]["hidden_dim"],
            ),
        )

    def forward(
        self, batch: Dict[str, torch.Tensor], mode: str = "train"
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播主函数

        Args:
            batch: 输入批次数据
            mode: 模式 ['train', 'eval', 'test']

        Returns:
            模型输出字典
        """
        features = batch["features"]
        labels = batch["labels"]
        views = batch["view"]
        sequence_lengths = batch["sequence_length"]

        # 1. 多视角特征融合
        fused_features = self._process_multi_view_fusion(features)

        # 2. 时序特征编码
        temporal_features = self._process_temporal_encoding(fused_features)

        # 3. 动作分类
        action_logits, action_predictions = self._process_action_classification(temporal_features)

        # 4. 任务图学习
        task_graph_outputs = self._process_task_graph(
            labels if mode == "train" else action_predictions,
            sequence_lengths
        )

        # 5. 原型学习
        prototype_outputs = self._process_prototype_learning(
            temporal_features,
            labels if mode == "train" else action_predictions
        )

        # 6. 时序一致性
        temporal_consistency_features = self.temporal_consistency_net(temporal_features)

        return self._build_outputs(
            action_logits, action_predictions, temporal_features,
            fused_features, task_graph_outputs, prototype_outputs,
            temporal_consistency_features, sequence_lengths, views
        )

    def _process_multi_view_fusion(self, features: torch.Tensor) -> torch.Tensor:
        """处理多视角特征融合"""
        view_features_list = [features]
        view_ids = [0] * len(view_features_list)

        fusion_outputs = self.multi_view_fusion(view_features_list, view_ids)
        return fusion_outputs["fused_features"]

    def _process_temporal_encoding(self, fused_features: torch.Tensor):
        """处理时序特征编码"""
        try:
            from torch.utils.checkpoint import checkpoint
            if self.training and hasattr(torch.utils.checkpoint, 'checkpoint'):
                def temporal_encoder_wrapper(x):
                    return self.temporal_encoder(x)[0]

                return checkpoint(temporal_encoder_wrapper, fused_features, use_reentrant=False)
            else:
                temporal_features, _ = self.temporal_encoder(fused_features)
                return temporal_features
        except (ImportError, RuntimeError):
            temporal_features, _ = self.temporal_encoder(fused_features)
            return temporal_features

    def _process_action_classification(self, temporal_features):
        """处理动作分类"""
        # 分块计算，以避免一次大矩阵乘法导致 CUBLAS 失败
        B, L, D = temporal_features.shape
        flat = temporal_features.reshape(-1, D)
        max_chunk = 50000
        parts = []
        for idx in range(0, flat.size(0), max_chunk):
            parts.append(self.action_classifier(flat[idx:idx + max_chunk]))
        action_logits = torch.cat(parts, dim=0).reshape(B, L, -1)
        action_predictions = torch.argmax(action_logits, dim=-1)
        return action_logits, action_predictions

    def _process_task_graph(self, action_sequences: torch.Tensor,
                           sequence_lengths: torch.Tensor):
        """处理任务图学习"""
        try:
            from torch.utils.checkpoint import checkpoint
            if self.training and hasattr(torch.utils.checkpoint, 'checkpoint'):
                def task_graph_wrapper(sequences, lengths):
                    return self.task_graph_encoder(sequences, lengths)

                return checkpoint(task_graph_wrapper, action_sequences, sequence_lengths, use_reentrant=False)
            else:
                return self.task_graph_encoder(action_sequences, sequence_lengths)
        except (ImportError, RuntimeError):
            return self.task_graph_encoder(action_sequences, sequence_lengths)

    def _process_prototype_learning(self, temporal_features, action_sequences):
        """处理原型学习"""
        return self.prototype_learner(
            temporal_features,
            action_sequences,
            update_prototypes=False,
        )

    def _build_outputs(self, action_logits, action_predictions,
                      temporal_features, fused_features,
                      task_graph_outputs, prototype_outputs,
                      temporal_consistency_features,
                      sequence_lengths, views):
        """构建输出字典"""
        return {
            "action_logits": action_logits,
            "action_predictions": action_predictions,
            "temporal_features": temporal_features,
            "fused_features": fused_features,
            "fusion_outputs": {"fused_features": fused_features},
            "task_graph_outputs": task_graph_outputs,
            "prototype_outputs": prototype_outputs,
            "temporal_consistency_features": temporal_consistency_features,
            "sequence_lengths": sequence_lengths,
            "views": views,
        }

    def compute_loss(
        self, outputs: Dict[str, torch.Tensor], batch: Dict[str, torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """计算总损失"""
        labels = batch["labels"]
        views = batch["view"]

        losses = {}

        # 1. 动作分类损失
        action_logits = outputs["action_logits"]
        valid_mask = labels >= 0  # 忽略填充标签

        if valid_mask.any():
            valid_logits = action_logits[valid_mask]
            valid_labels = labels[valid_mask]
            action_loss = F.cross_entropy(valid_logits, valid_labels)
            losses["action_classification"] = (
                action_loss
                * self.config["training"]["loss_weights"]["action_classification"]
            )

        # 2. 原型对比学习损失
        prototype_outputs = outputs["prototype_outputs"]
        similarities = prototype_outputs.get("similarities", torch.tensor(0.0))
        # 构造原型到动作映射张量以支持 torch.compile 优化
        num_prototypes = self.prototype_learner.prototypes.size(0)
        prototype_to_action_map = torch.tensor(
            [self.prototype_learner.prototype_to_action[i] for i in range(num_prototypes)],
            device=similarities.device, dtype=torch.long
        )
        prototype_loss = self.prototype_loss(similarities, labels, prototype_to_action_map)
        losses["prototype_contrastive"] = (
            prototype_loss
            * self.config["training"]["loss_weights"]["prototype_contrastive"]
        )

        # 3. 任务图结构损失
        # 构建任务图损失的目标字典，包含动作序列用于时序损失计算
        task_graph_targets = {
            'action_sequences': labels  # 使用真实标签序列计算时序一致性损失
        }
        task_graph_losses = self.task_graph_loss(
            outputs["task_graph_outputs"], task_graph_targets
        )
        for key, value in task_graph_losses.items():
            losses[f"task_graph_{key}"] = value

        # 4. 多视角学习损失
        view_ids = [0] * len(views)  # 简化处理
        multi_view_losses = self.multi_view_loss(outputs["fusion_outputs"], view_ids)
        for key, value in multi_view_losses.items():
            losses[f"multi_view_{key}"] = value

        # 5. 时序一致性损失
        temporal_consistency_loss = self._compute_temporal_consistency_loss(
            outputs["temporal_features"],
            outputs["temporal_consistency_features"],
            outputs["sequence_lengths"],
        )
        losses["temporal_consistency"] = (
            temporal_consistency_loss
            * self.config["training"]["loss_weights"]["temporal_consistency"]
        )

        # 总损失
        total_loss = sum(losses.values())
        losses["total_loss"] = total_loss

        return losses

    def _compute_temporal_consistency_loss(
        self,
        temporal_features: torch.Tensor,
        consistency_features: torch.Tensor,
        sequence_lengths: torch.Tensor,
    ) -> torch.Tensor:
        """计算时序一致性损失"""
        batch_size = temporal_features.size(0)
        total_loss = 0.0
        valid_samples = 0

        for i in range(batch_size):
            seq_len = sequence_lengths[i].item()
            if seq_len > 1:
                # 计算相邻帧的特征差异
                temp_feat = temporal_features[i, : seq_len - 1]
                cons_feat = consistency_features[i, : seq_len - 1]

                # 平滑性约束
                temp_diff = temp_feat[1:] - temp_feat[:-1]
                cons_diff = cons_feat[1:] - cons_feat[:-1]

                smoothness_loss = F.mse_loss(temp_diff, cons_diff)
                total_loss += smoothness_loss
                valid_samples += 1

        if valid_samples > 0:
            # 修复UserWarning: 使用new_tensor()替代torch.tensor()
            loss_value = total_loss / valid_samples
            return temporal_features.new_tensor([loss_value])
        else:
            return temporal_features.new_tensor([0.0])

    def extract_task_graph(self, threshold: float = 0.5) -> Dict:
        """提取学习到的静态任务图"""
        # 获取所有动作的原型特征
        prototype_centers = self.prototype_learner.compute_prototype_centers()

        # 创建动作序列用于图构建
        action_sequence = torch.arange(self.num_actions).unsqueeze(0)
        sequence_length = torch.tensor([self.num_actions])

        # 通过任务图编码器
        with torch.no_grad():
            task_graph_outputs = self.task_graph_encoder(
                action_sequence, sequence_length
            )
            edge_probs = task_graph_outputs["edge_probabilities"][
                0
            ]  # 第一个（也是唯一一个）图

        # 构建邻接矩阵
        adjacency_matrix = (edge_probs > threshold).float()

        return {
            "adjacency_matrix": adjacency_matrix,
            "edge_probabilities": edge_probs,
            "prototype_centers": prototype_centers,
            "action_embeddings": task_graph_outputs["node_embeddings"],
        }

    def get_action_prototypes(self) -> Dict[int, torch.Tensor]:
        """获取所有动作的原型特征"""
        prototypes = {}
        for action_id in range(self.num_actions):
            prototypes[action_id] = self.prototype_learner.get_action_prototypes(
                action_id
            )
        return prototypes
