"""
多视角特征融合模块
处理不同视角间的特征差异，实现跨视角的特征对齐和融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.autograd
from typing import Dict, List, Tuple


class ViewEncoder(nn.Module):
    """视角特定编码器"""
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int = 512,
                 output_dim: int = 256):
        super().__init__()
        
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, output_dim),
            nn.LayerNorm(output_dim)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.encoder(x)


class DomainAdversarialNetwork(nn.Module):
    """域对抗网络，用于视角不变特征学习"""
    
    def __init__(self, 
                 feature_dim: int,
                 num_views: int,
                 hidden_dim: int = 256):
        super().__init__()
        
        self.discriminator = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, num_views)
        )
        
        # 梯度反转层的缩放因子
        self.lambda_factor = 1.0
    
    def forward(self, features: torch.Tensor, alpha: float = 1.0) -> torch.Tensor:
        # 梯度反转
        reversed_features = GradientReversalFunction.apply(features, alpha)
        return self.discriminator(reversed_features)


class GradientReversalFunction(torch.autograd.Function):
    """梯度反转函数"""
    
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.alpha = alpha
        return x.view_as(x)
    
    @staticmethod
    def backward(ctx, grad_output):
        output = grad_output.neg() * ctx.alpha
        return output, None


class SimpleFusion(nn.Module):
    """简化的多视角融合（不使用注意力机制）"""

    def __init__(self,
                 feature_dim: int,
                 fusion_method: str = "mean",
                 dropout: float = 0.1):
        super().__init__()

        self.fusion_method = fusion_method
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(feature_dim)

        # 如果使用concat方法，需要降维
        if fusion_method == "concat":
            # 动态设置，支持不同数量的视角
            self.max_views = 4  # 最多支持4个视角
            self.projection = None  # 延迟初始化

    def forward(self,
                view_features: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            view_features: 每个视角的特征列表 [batch, seq_len, feature_dim]
            view_masks: 每个视角的掩码列表（可选）

        Returns:
            融合后的特征 [batch, seq_len, feature_dim]
        """
        if not view_features:
            raise ValueError("view_features cannot be empty")

        if self.fusion_method == "mean":
            # 简单平均融合
            stacked_features = torch.stack(view_features, dim=0)  # [num_views, batch, seq_len, feature_dim]
            output = stacked_features.mean(dim=0)  # [batch, seq_len, feature_dim]

        elif self.fusion_method == "concat":
            # 拼接融合
            concatenated = torch.cat(view_features, dim=-1)  # [batch, seq_len, feature_dim * num_views]

            # 延迟初始化projection层
            if self.projection is None:
                input_dim = concatenated.shape[-1]
                output_dim = view_features[0].shape[-1]
                self.projection = nn.Linear(input_dim, output_dim).to(concatenated.device)

            output = self.projection(concatenated)  # [batch, seq_len, feature_dim]

        else:  # 默认使用mean
            stacked_features = torch.stack(view_features, dim=0)
            output = stacked_features.mean(dim=0)

        # 应用dropout和层归一化
        output = self.dropout(output)
        output = self.layer_norm(output)

        return output


class UncertaintyWeighting(nn.Module):
    """基于不确定性的视角权重学习"""
    
    def __init__(self, 
                 feature_dim: int,
                 num_views: int):
        super().__init__()
        
        self.num_views = num_views
        
        # 不确定性估计网络
        self.uncertainty_net = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # 视角权重网络
        self.weight_net = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, num_views),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, view_features: List[torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            view_features: 每个视角的特征列表
            
        Returns:
            融合特征和不确定性权重
        """
        _ = view_features[0].shape
        
        # 计算每个视角的不确定性
        uncertainties = []
        for view_feat in view_features:
            uncertainty = self.uncertainty_net(view_feat)  # [batch, seq, 1]
            uncertainties.append(uncertainty)
        
        uncertainties = torch.cat(uncertainties, dim=-1)  # [batch, seq, num_views]
        
        # 基于不确定性计算权重（不确定性越低，权重越高）
        confidence = 1.0 - uncertainties
        weights = F.softmax(confidence, dim=-1)
        
        # 加权融合
        stacked_features = torch.stack(view_features, dim=-1)  # [batch, seq, feat, num_views]
        weights_expanded = weights.unsqueeze(2)  # [batch, seq, 1, num_views]
        
        fused_features = torch.sum(stacked_features * weights_expanded, dim=-1)
        
        return fused_features, weights


class MultiViewFusion(nn.Module):
    """多视角融合主模块"""
    
    def __init__(self,
                 input_dim: int,
                 hidden_dim: int = 512,
                 output_dim: int = 256,
                 num_views: int = 3,
                 fusion_method: str = "mean",
                 domain_adaptation: bool = True,
                 uncertainty_weighting: bool = True):
        """
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            output_dim: 输出特征维度
            num_views: 视角数量
            fusion_method: 融合方法 ["concat", "mean", "gated"]
            domain_adaptation: 是否使用域适应
            uncertainty_weighting: 是否使用不确定性权重
        """
        super().__init__()
        
        self.num_views = num_views
        self.fusion_method = fusion_method
        self.domain_adaptation = domain_adaptation
        self.uncertainty_weighting = uncertainty_weighting
        
        # 视角特定编码器
        self.view_encoders = nn.ModuleList([
            ViewEncoder(input_dim, hidden_dim, output_dim) 
            for _ in range(num_views)
        ])
        
        # 融合模块（简化版本）
        if fusion_method == "concat":
            self.fusion_module = nn.Sequential(
                nn.Linear(output_dim * num_views, output_dim),
                nn.ReLU(),
                nn.LayerNorm(output_dim)
            )
        elif fusion_method == "gated":
            self.gate_net = nn.Sequential(
                nn.Linear(output_dim * num_views, num_views),
                nn.Sigmoid()
            )
        else:  # mean 或其他，使用SimpleFusion
            self.fusion_module = SimpleFusion(output_dim, fusion_method)
        
        # 域对抗网络
        if domain_adaptation:
            self.domain_discriminator = DomainAdversarialNetwork(output_dim, num_views)
        
        # 不确定性权重
        if uncertainty_weighting:
            self.uncertainty_weighting_module = UncertaintyWeighting(output_dim, num_views)
    
    def forward(self, 
                view_features_list: List[torch.Tensor],
                view_ids: List[int],
                alpha: float = 1.0) -> Dict[str, torch.Tensor]:
        """
        Args:
            view_features_list: 每个视角的特征列表
            view_ids: 视角ID列表
            alpha: 域适应的权重参数
            
        Returns:
            融合结果字典
        """
        # 视角特定编码
        encoded_features = []
        for features, view_id in zip(view_features_list, view_ids):
            if view_id < len(self.view_encoders):
                encoded = self.view_encoders[view_id](features)
                encoded_features.append(encoded)
        
        if not encoded_features:
            raise ValueError("No valid view features provided")
        
        # 特征融合（简化版本）
        if self.fusion_method == "concat" and len(encoded_features) > 1:
            # 连接所有视角特征
            concatenated = torch.cat(encoded_features, dim=-1)
            fused_features = self.fusion_module(concatenated)
        elif self.fusion_method == "gated" and len(encoded_features) > 1:
            # 门控融合
            concatenated = torch.cat(encoded_features, dim=-1)
            gates = self.gate_net(concatenated)

            # 应用门控权重
            stacked_features = torch.stack(encoded_features, dim=-1)
            gates_expanded = gates.unsqueeze(2)
            fused_features = torch.sum(stacked_features * gates_expanded, dim=-1)
        else:  # mean 或单视角情况
            if len(encoded_features) == 1:
                fused_features = encoded_features[0]
            else:
                fused_features = self.fusion_module(encoded_features)
        
        results = {
            'fused_features': fused_features,
            'encoded_features': encoded_features
        }
        
        # 不确定性权重
        if self.uncertainty_weighting:
            uncertainty_fused, uncertainty_weights = self.uncertainty_weighting_module(encoded_features)
            results['uncertainty_fused'] = uncertainty_fused
            results['uncertainty_weights'] = uncertainty_weights
        
        # 域判别
        if self.domain_adaptation:
            domain_predictions = []
            for features in encoded_features:
                domain_pred = self.domain_discriminator(features, alpha)
                domain_predictions.append(domain_pred)
            results['domain_predictions'] = domain_predictions
        
        return results


class MultiViewLoss(nn.Module):
    """多视角学习的损失函数"""
    
    def __init__(self,
                 domain_weight: float = 0.1,
                 uncertainty_weight: float = 0.05):
        super().__init__()
        self.domain_weight = domain_weight
        self.uncertainty_weight = uncertainty_weight
        
    def forward(self, 
                predictions: Dict[str, torch.Tensor],
                view_ids: List[int]) -> Dict[str, torch.Tensor]:
        """计算多视角学习损失"""
        losses = {}
        
        # 域适应损失
        if 'domain_predictions' in predictions:
            domain_loss = 0.0
            for i, domain_pred in enumerate(predictions['domain_predictions']):
                if i < len(view_ids):
                    target_view = torch.full((domain_pred.size(0), domain_pred.size(1)), 
                                           view_ids[i], 
                                           dtype=torch.long, 
                                           device=domain_pred.device)
                    domain_loss += F.cross_entropy(
                        domain_pred.view(-1, domain_pred.size(-1)),
                        target_view.view(-1)
                    )
            
            if len(predictions['domain_predictions']) > 0:
                domain_loss /= len(predictions['domain_predictions'])
                losses['domain_loss'] = domain_loss * self.domain_weight
        
        # 不确定性正则化损失
        if 'uncertainty_weights' in predictions:
            uncertainty_weights = predictions['uncertainty_weights']
            # 鼓励权重分布的多样性，避免过度依赖单一视角
            entropy_loss = -torch.mean(torch.sum(uncertainty_weights * torch.log(uncertainty_weights + 1e-8), dim=-1))
            losses['uncertainty_loss'] = entropy_loss * self.uncertainty_weight
        
        return losses



