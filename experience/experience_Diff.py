#!/usr/bin/env python3
"""
差分任务图模型推理和分析模块
加载训练完成的差分模型，执行推理并生成学术报告和可视化图表
"""

import argparse
import os
import sys
import json
import torch
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime

# 项目根目录设置
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# 确保工作目录正确
if os.getcwd() != str(ROOT_DIR):
    os.chdir(ROOT_DIR)

# 输出目录配置
OUTPUT_DIR = Path("/data2/syd_data/Breakfast_Data/Outputs")
CHECKPOINT_DIR = OUTPUT_DIR / "checkpoints"
EXPERIENCE_DIR = OUTPUT_DIR / "experience"
REPORT_DIR = EXPERIENCE_DIR / "reports"
VIS_DIR = EXPERIENCE_DIR / "visualizations"


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="差分任务图模型推理和分析"
    )
    parser.add_argument(
        "--model-path",
        default=str(CHECKPOINT_DIR / "diff_model.pth"),
        help="差分模型路径"
    )
    parser.add_argument(
        "--config",
        default="configs/default.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--output-dir",
        default=str(EXPERIENCE_DIR),
        help="输出目录"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=32,
        help="推理批处理大小"
    )
    
    return parser.parse_args()


def load_differential_model(model_path: str, device: torch.device) -> Tuple[Any, Dict, str]:
    """
    加载训练完成的差分模型
    
    Args:
        model_path: 差分模型文件路径
        device: 计算设备
        
    Returns:
        (差分模型, 配置信息, 静态模型路径)
    """
    print(f"📥 加载差分模型: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"差分模型文件不存在: {model_path}")
    
    # 加载差分模型检查点
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # 获取配置信息
    config = checkpoint.get('config', {})
    optimization_config = checkpoint.get('optimization_config', {})
    static_model_path = checkpoint.get('static_model_path', '')
    
    print(f"📥 加载静态模型: {static_model_path}")
    
    # 首先加载静态模型
    from src.model import ProceduralVideoUnderstanding
    static_model = ProceduralVideoUnderstanding(config).to(device)
    
    # 加载静态模型权重
    if os.path.exists(static_model_path):
        static_checkpoint = torch.load(static_model_path, map_location=device, weights_only=False)
        static_model.load_state_dict(static_checkpoint['model_state_dict'])
    else:
        print(f"⚠️ 静态模型文件不存在: {static_model_path}")
    
    # 创建差分模型（需要导入差分模型类）
    # 这里需要从train_Diff.py中导入DifferentialModel类
    sys.path.append(str(ROOT_DIR / "scripts"))
    from train_Diff import DifferentialModel
    
    # 创建差分模型实例
    diff_model = DifferentialModel(static_model, config).to(device)
    
    # 加载差分模型权重
    diff_model.load_state_dict(checkpoint['model_state_dict'])
    diff_model.eval()
    
    print(f"✅ 差分模型加载成功")
    print(f"   - 训练轮数: {checkpoint.get('epoch', 'unknown')}")
    print(f"   - 模型准确率: {checkpoint.get('accuracy', 'unknown'):.4f}")
    print(f"   - 静态模型路径: {static_model_path}")
    
    return diff_model, config, static_model_path


def perform_differential_inference(model: Any, test_loader, device: torch.device) -> Dict[str, Any]:
    """
    执行差分模型推理
    
    Args:
        model: 差分模型
        test_loader: 测试数据加载器
        device: 计算设备
        
    Returns:
        推理结果字典
    """
    print("🔍 执行差分模型推理...")
    
    model.eval()
    results = {
        'fused_predictions': [],
        'static_predictions': [],
        'dynamic_predictions': [],
        'ground_truth': [],
        'diff_features': [],
        'weight_adjustments': []
    }
    
    total_samples = 0
    fused_correct = 0
    static_correct = 0
    dynamic_correct = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            # 移动数据到设备
            for key in ["features", "labels"]:
                if key in batch:
                    batch[key] = batch[key].to(device, non_blocking=True)
            
            # 差分模型推理
            outputs = model(batch, mode="eval")
            
            # 获取各种预测结果
            fused_predictions = outputs["action_predictions"]
            static_predictions = outputs["static_action_predictions"]
            dynamic_predictions = outputs["dynamic_graph_predictions"]
            labels = batch["labels"]
            
            # 统计准确率
            valid_mask = labels >= 0
            if valid_mask.any():
                fused_correct += (fused_predictions[valid_mask] == labels[valid_mask]).sum().item()
                static_correct += (static_predictions[valid_mask] == labels[valid_mask]).sum().item()
                dynamic_correct += (dynamic_predictions[valid_mask] == labels[valid_mask]).sum().item()
                total_samples += valid_mask.sum().item()
            
            # 保存详细结果
            results['fused_predictions'].extend(fused_predictions.cpu().numpy().tolist())
            results['static_predictions'].extend(static_predictions.cpu().numpy().tolist())
            results['dynamic_predictions'].extend(dynamic_predictions.cpu().numpy().tolist())
            results['ground_truth'].extend(labels.cpu().numpy().tolist())
            
            # 保存差分特征和权重调整
            if 'diff_features' in outputs:
                results['diff_features'].extend(
                    outputs['diff_features'].cpu().numpy().tolist()
                )
            if 'delta_W_raw' in outputs:
                results['weight_adjustments'].extend(
                    outputs['delta_W_raw'].cpu().numpy().tolist()
                )
            
            if batch_idx % 10 == 0:
                fused_acc = fused_correct / total_samples if total_samples > 0 else 0
                static_acc = static_correct / total_samples if total_samples > 0 else 0
                dynamic_acc = dynamic_correct / total_samples if total_samples > 0 else 0
                print(f"   Batch {batch_idx}: 融合={fused_acc:.4f}, 静态={static_acc:.4f}, 动态={dynamic_acc:.4f}")
    
    # 计算最终指标
    fused_accuracy = fused_correct / total_samples if total_samples > 0 else 0
    static_accuracy = static_correct / total_samples if total_samples > 0 else 0
    dynamic_accuracy = dynamic_correct / total_samples if total_samples > 0 else 0
    
    results.update({
        'fused_accuracy': fused_accuracy,
        'static_accuracy': static_accuracy,
        'dynamic_accuracy': dynamic_accuracy,
        'total_samples': total_samples,
        'fused_correct': fused_correct,
        'static_correct': static_correct,
        'dynamic_correct': dynamic_correct
    })
    
    print(f"✅ 差分模型推理完成")
    print(f"   - 总样本数: {total_samples}")
    print(f"   - 融合模型准确率: {fused_accuracy:.4f}")
    print(f"   - 静态模型准确率: {static_accuracy:.4f}")
    print(f"   - 动态图准确率: {dynamic_accuracy:.4f}")
    
    return results


def generate_differential_analysis_report(results: Dict[str, Any], config: Dict, 
                                        model_path: str, static_model_path: str, 
                                        output_dir: Path) -> None:
    """
    生成差分模型分析报告
    
    Args:
        results: 推理结果
        config: 模型配置
        model_path: 差分模型路径
        static_model_path: 静态模型路径
        output_dir: 输出目录
    """
    print("📊 生成差分模型分析报告...")
    
    try:
        # 创建输出目录
        report_dir = output_dir / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成Markdown报告
        report_path = report_dir / f"differential_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 差分任务图模型分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**差分模型路径**: {model_path}\n\n")
            f.write(f"**静态模型路径**: {static_model_path}\n\n")
            
            f.write("## 模型性能对比\n\n")
            f.write(f"- **总样本数**: {results['total_samples']:,}\n")
            f.write(f"- **融合模型准确率**: {results['fused_accuracy']:.4f} ({results['fused_accuracy']*100:.2f}%)\n")
            f.write(f"- **静态模型准确率**: {results['static_accuracy']:.4f} ({results['static_accuracy']*100:.2f}%)\n")
            f.write(f"- **动态图准确率**: {results['dynamic_accuracy']:.4f} ({results['dynamic_accuracy']*100:.2f}%)\n\n")
            
            # 计算性能提升
            improvement_over_static = results['fused_accuracy'] - results['static_accuracy']
            improvement_over_dynamic = results['fused_accuracy'] - results['dynamic_accuracy']
            
            f.write("## 性能提升分析\n\n")
            f.write(f"- **相对静态模型提升**: {improvement_over_static:.4f} ({improvement_over_static*100:.2f}%)\n")
            f.write(f"- **相对动态图提升**: {improvement_over_dynamic:.4f} ({improvement_over_dynamic*100:.2f}%)\n\n")
            
            f.write("## 差分模型架构信息\n\n")
            f.write(f"- **差分特征维度**: 256\n")
            f.write(f"- **权重调整MLP**: 512隐藏层\n")
            f.write(f"- **动态图管理器**: α=0.1\n")
            f.write(f"- **融合网络**: 双路径融合\n\n")
            
            f.write("## 推理统计信息\n\n")
            f.write(f"- **融合预测样本数**: {len(results['fused_predictions'])}\n")
            f.write(f"- **静态预测样本数**: {len(results['static_predictions'])}\n")
            f.write(f"- **动态预测样本数**: {len(results['dynamic_predictions'])}\n")
            f.write(f"- **差分特征样本数**: {len(results['diff_features'])}\n")
            f.write(f"- **权重调整样本数**: {len(results['weight_adjustments'])}\n")
            
        # 保存JSON格式的详细结果
        json_path = report_dir / f"differential_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({
                'model_path': model_path,
                'static_model_path': static_model_path,
                'config': config,
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 差分模型分析报告已生成:")
        print(f"   📄 Markdown报告: {report_path}")
        print(f"   📋 JSON结果: {json_path}")
        
    except Exception as e:
        print(f"⚠️ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 差分任务图模型推理和分析")
    print("=" * 60)
    
    # 解析参数
    args = parse_args()
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        for directory in [REPORT_DIR, VIS_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 初始化设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🔧 使用设备: {device}")
        
        # 加载差分模型
        model, config, static_model_path = load_differential_model(args.model_path, device)
        
        # 创建数据加载器
        print("🏗️ 创建数据加载器...")
        import yaml
        with open(args.config, "r", encoding="utf-8") as f:
            data_config = yaml.safe_load(f)
        
        # 更新批处理大小
        data_config["training"]["batch_size"] = args.batch_size
        
        from src.data_loader_fixed import create_data_loaders
        _, test_loader = create_data_loaders(data_config)
        
        # 执行推理
        results = perform_differential_inference(model, test_loader, device)
        
        # 生成分析报告
        generate_differential_analysis_report(results, config, args.model_path, 
                                            static_model_path, output_dir)
        
        print(f"\n🎉 差分模型分析完成！")
        print(f"   输出目录: {output_dir}")
        
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
