#!/usr/bin/env python3
"""
静态与差分任务图模型对比分析模块
同时加载静态和差分模型，执行对比分析并生成综合报告
"""

import argparse
import os
import sys
import json
import torch
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime

# 项目根目录设置
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# 确保工作目录正确
if os.getcwd() != str(ROOT_DIR):
    os.chdir(ROOT_DIR)

# 输出目录配置
OUTPUT_DIR = Path("/data2/syd_data/Breakfast_Data/Outputs")
CHECKPOINT_DIR = OUTPUT_DIR / "checkpoints"
EXPERIENCE_DIR = OUTPUT_DIR / "experience"
REPORT_DIR = EXPERIENCE_DIR / "reports"
VIS_DIR = EXPERIENCE_DIR / "visualizations"


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="静态与差分任务图模型对比分析"
    )
    parser.add_argument(
        "--static-model",
        default=str(CHECKPOINT_DIR / "static_model.pth"),
        help="静态模型路径"
    )
    parser.add_argument(
        "--diff-model",
        default=str(CHECKPOINT_DIR / "diff_model.pth"),
        help="差分模型路径"
    )
    parser.add_argument(
        "--config",
        default="configs/default.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--output-dir",
        default=str(EXPERIENCE_DIR),
        help="输出目录"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=32,
        help="推理批处理大小"
    )
    
    return parser.parse_args()


def load_models(static_model_path: str, diff_model_path: str, device: torch.device) -> Tuple[Any, Any, Dict]:
    """
    加载静态模型和差分模型
    
    Args:
        static_model_path: 静态模型路径
        diff_model_path: 差分模型路径
        device: 计算设备
        
    Returns:
        (静态模型, 差分模型, 配置信息)
    """
    print(f"📥 加载静态模型: {static_model_path}")
    print(f"📥 加载差分模型: {diff_model_path}")
    
    # 检查文件存在性
    if not os.path.exists(static_model_path):
        raise FileNotFoundError(f"静态模型文件不存在: {static_model_path}")
    if not os.path.exists(diff_model_path):
        raise FileNotFoundError(f"差分模型文件不存在: {diff_model_path}")
    
    # 加载静态模型
    static_checkpoint = torch.load(static_model_path, map_location=device, weights_only=False)
    static_config = static_checkpoint.get('config', {})
    
    from src.model import ProceduralVideoUnderstanding
    static_model = ProceduralVideoUnderstanding(static_config).to(device)
    static_model.load_state_dict(static_checkpoint['model_state_dict'])
    static_model.eval()
    
    # 加载差分模型
    diff_checkpoint = torch.load(diff_model_path, map_location=device, weights_only=False)
    diff_config = diff_checkpoint.get('config', {})
    
    # 创建差分模型（需要导入差分模型类）
    sys.path.append(str(ROOT_DIR / "scripts"))
    from train_Diff import DifferentialModel
    
    # 创建差分模型的静态基础模型
    diff_static_model = ProceduralVideoUnderstanding(diff_config).to(device)
    diff_static_model.load_state_dict(static_checkpoint['model_state_dict'])
    
    # 创建差分模型实例
    diff_model = DifferentialModel(diff_static_model, diff_config).to(device)
    diff_model.load_state_dict(diff_checkpoint['model_state_dict'])
    diff_model.eval()
    
    print(f"✅ 模型加载成功")
    print(f"   - 静态模型准确率: {static_checkpoint.get('accuracy', 'unknown'):.4f}")
    print(f"   - 差分模型准确率: {diff_checkpoint.get('accuracy', 'unknown'):.4f}")
    
    return static_model, diff_model, static_config


def perform_comparative_inference(static_model: Any, diff_model: Any, test_loader, device: torch.device) -> Dict[str, Any]:
    """
    执行对比推理分析
    
    Args:
        static_model: 静态模型
        diff_model: 差分模型
        test_loader: 测试数据加载器
        device: 计算设备
        
    Returns:
        对比分析结果字典
    """
    print("🔍 执行对比推理分析...")
    
    static_model.eval()
    diff_model.eval()
    
    results = {
        'static_predictions': [],
        'diff_fused_predictions': [],
        'diff_static_predictions': [],
        'diff_dynamic_predictions': [],
        'ground_truth': [],
        'agreement_analysis': [],
        'performance_comparison': []
    }
    
    total_samples = 0
    static_correct = 0
    diff_fused_correct = 0
    diff_static_correct = 0
    diff_dynamic_correct = 0
    agreement_count = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            # 移动数据到设备
            for key in ["features", "labels"]:
                if key in batch:
                    batch[key] = batch[key].to(device, non_blocking=True)
            
            # 静态模型推理
            static_outputs = static_model(batch, mode="eval")
            static_predictions = torch.argmax(static_outputs["action_logits"], dim=-1)
            
            # 差分模型推理
            diff_outputs = diff_model(batch, mode="eval")
            diff_fused_predictions = diff_outputs["action_predictions"]
            diff_static_predictions = diff_outputs["static_action_predictions"]
            diff_dynamic_predictions = diff_outputs["dynamic_graph_predictions"]
            
            labels = batch["labels"]
            
            # 统计准确率
            valid_mask = labels >= 0
            if valid_mask.any():
                static_correct += (static_predictions[valid_mask] == labels[valid_mask]).sum().item()
                diff_fused_correct += (diff_fused_predictions[valid_mask] == labels[valid_mask]).sum().item()
                diff_static_correct += (diff_static_predictions[valid_mask] == labels[valid_mask]).sum().item()
                diff_dynamic_correct += (diff_dynamic_predictions[valid_mask] == labels[valid_mask]).sum().item()
                
                # 统计一致性
                agreement_count += (static_predictions[valid_mask] == diff_fused_predictions[valid_mask]).sum().item()
                total_samples += valid_mask.sum().item()
            
            # 保存预测结果
            results['static_predictions'].extend(static_predictions.cpu().numpy().tolist())
            results['diff_fused_predictions'].extend(diff_fused_predictions.cpu().numpy().tolist())
            results['diff_static_predictions'].extend(diff_static_predictions.cpu().numpy().tolist())
            results['diff_dynamic_predictions'].extend(diff_dynamic_predictions.cpu().numpy().tolist())
            results['ground_truth'].extend(labels.cpu().numpy().tolist())
            
            if batch_idx % 10 == 0:
                static_acc = static_correct / total_samples if total_samples > 0 else 0
                diff_acc = diff_fused_correct / total_samples if total_samples > 0 else 0
                agreement_rate = agreement_count / total_samples if total_samples > 0 else 0
                print(f"   Batch {batch_idx}: 静态={static_acc:.4f}, 差分={diff_acc:.4f}, 一致性={agreement_rate:.4f}")
    
    # 计算最终指标
    static_accuracy = static_correct / total_samples if total_samples > 0 else 0
    diff_fused_accuracy = diff_fused_correct / total_samples if total_samples > 0 else 0
    diff_static_accuracy = diff_static_correct / total_samples if total_samples > 0 else 0
    diff_dynamic_accuracy = diff_dynamic_correct / total_samples if total_samples > 0 else 0
    agreement_rate = agreement_count / total_samples if total_samples > 0 else 0
    
    results.update({
        'static_accuracy': static_accuracy,
        'diff_fused_accuracy': diff_fused_accuracy,
        'diff_static_accuracy': diff_static_accuracy,
        'diff_dynamic_accuracy': diff_dynamic_accuracy,
        'agreement_rate': agreement_rate,
        'total_samples': total_samples,
        'improvement': diff_fused_accuracy - static_accuracy
    })
    
    print(f"✅ 对比推理分析完成")
    print(f"   - 总样本数: {total_samples}")
    print(f"   - 静态模型准确率: {static_accuracy:.4f}")
    print(f"   - 差分融合准确率: {diff_fused_accuracy:.4f}")
    print(f"   - 性能提升: {results['improvement']:.4f}")
    print(f"   - 预测一致性: {agreement_rate:.4f}")
    
    return results


def generate_comparative_analysis_report(results: Dict[str, Any], config: Dict, 
                                       static_model_path: str, diff_model_path: str, 
                                       output_dir: Path) -> None:
    """
    生成对比分析报告
    
    Args:
        results: 对比分析结果
        config: 模型配置
        static_model_path: 静态模型路径
        diff_model_path: 差分模型路径
        output_dir: 输出目录
    """
    print("📊 生成对比分析报告...")
    
    try:
        # 创建输出目录
        report_dir = output_dir / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成Markdown报告
        report_path = report_dir / f"comparative_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 静态与差分任务图模型对比分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**静态模型路径**: {static_model_path}\n\n")
            f.write(f"**差分模型路径**: {diff_model_path}\n\n")
            
            f.write("## 性能对比总结\n\n")
            f.write(f"- **总样本数**: {results['total_samples']:,}\n")
            f.write(f"- **静态模型准确率**: {results['static_accuracy']:.4f} ({results['static_accuracy']*100:.2f}%)\n")
            f.write(f"- **差分融合准确率**: {results['diff_fused_accuracy']:.4f} ({results['diff_fused_accuracy']*100:.2f}%)\n")
            f.write(f"- **差分静态准确率**: {results['diff_static_accuracy']:.4f} ({results['diff_static_accuracy']*100:.2f}%)\n")
            f.write(f"- **差分动态准确率**: {results['diff_dynamic_accuracy']:.4f} ({results['diff_dynamic_accuracy']*100:.2f}%)\n\n")
            
            f.write("## 关键发现\n\n")
            improvement = results['improvement']
            improvement_percent = improvement * 100
            
            f.write(f"### 性能提升分析\n")
            f.write(f"- **绝对提升**: {improvement:.4f}\n")
            f.write(f"- **相对提升**: {improvement_percent:.2f}%\n")
            
            if improvement > 0:
                f.write(f"- **结论**: 差分模型相比静态模型有显著性能提升\n\n")
            elif improvement < 0:
                f.write(f"- **结论**: 差分模型性能略低于静态模型，需要进一步优化\n\n")
            else:
                f.write(f"- **结论**: 差分模型与静态模型性能相当\n\n")
            
            f.write(f"### 预测一致性分析\n")
            f.write(f"- **一致性比率**: {results['agreement_rate']:.4f} ({results['agreement_rate']*100:.2f}%)\n")
            
            if results['agreement_rate'] > 0.8:
                f.write(f"- **结论**: 两个模型预测高度一致\n\n")
            elif results['agreement_rate'] > 0.6:
                f.write(f"- **结论**: 两个模型预测中等一致\n\n")
            else:
                f.write(f"- **结论**: 两个模型预测差异较大\n\n")
            
            f.write("## 技术架构对比\n\n")
            f.write("| 特性 | 静态模型 | 差分模型 |\n")
            f.write("|------|----------|----------|\n")
            f.write("| 任务图类型 | 静态固定 | 动态调整 |\n")
            f.write("| 执行偏差建模 | 无 | 有 |\n")
            f.write("| 权重调整机制 | 无 | MLP_diff |\n")
            f.write("| 融合策略 | 单一预测 | 双路径融合 |\n")
            f.write("| 适应性 | 低 | 高 |\n\n")
            
            f.write("## 推理统计信息\n\n")
            f.write(f"- **静态预测样本数**: {len(results['static_predictions'])}\n")
            f.write(f"- **差分融合预测样本数**: {len(results['diff_fused_predictions'])}\n")
            f.write(f"- **差分静态预测样本数**: {len(results['diff_static_predictions'])}\n")
            f.write(f"- **差分动态预测样本数**: {len(results['diff_dynamic_predictions'])}\n")
            
        # 保存JSON格式的详细结果
        json_path = report_dir / f"comparative_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({
                'static_model_path': static_model_path,
                'diff_model_path': diff_model_path,
                'config': config,
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 对比分析报告已生成:")
        print(f"   📄 Markdown报告: {report_path}")
        print(f"   📋 JSON结果: {json_path}")
        
    except Exception as e:
        print(f"⚠️ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 静态与差分任务图模型对比分析")
    print("=" * 60)
    
    # 解析参数
    args = parse_args()
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        for directory in [REPORT_DIR, VIS_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 初始化设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🔧 使用设备: {device}")
        
        # 加载两个模型
        static_model, diff_model, config = load_models(args.static_model, args.diff_model, device)
        
        # 创建数据加载器
        print("🏗️ 创建数据加载器...")
        import yaml
        with open(args.config, "r", encoding="utf-8") as f:
            data_config = yaml.safe_load(f)
        
        # 更新批处理大小
        data_config["training"]["batch_size"] = args.batch_size
        
        from src.data_loader_fixed import create_data_loaders
        _, test_loader = create_data_loaders(data_config)
        
        # 执行对比推理
        results = perform_comparative_inference(static_model, diff_model, test_loader, device)
        
        # 生成对比分析报告
        generate_comparative_analysis_report(results, config, args.static_model, 
                                           args.diff_model, output_dir)
        
        print(f"\n🎉 对比分析完成！")
        print(f"   输出目录: {output_dir}")
        
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
