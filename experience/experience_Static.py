#!/usr/bin/env python3
"""
静态任务图模型推理和分析模块
加载训练完成的静态模型，执行推理并生成学术报告和可视化图表
"""

import argparse
import os
import sys
import json
import torch
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime

# 项目根目录设置
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# 确保工作目录正确
if os.getcwd() != str(ROOT_DIR):
    os.chdir(ROOT_DIR)

# 输出目录配置
OUTPUT_DIR = Path("/data2/syd_data/Breakfast_Data/Outputs")
CHECKPOINT_DIR = OUTPUT_DIR / "checkpoints"
EXPERIENCE_DIR = OUTPUT_DIR / "experience"
REPORT_DIR = EXPERIENCE_DIR / "reports"
VIS_DIR = EXPERIENCE_DIR / "visualizations"


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="静态任务图模型推理和分析"
    )
    parser.add_argument(
        "--model-path",
        default=str(CHECKPOINT_DIR / "static_model.pth"),
        help="静态模型路径"
    )
    parser.add_argument(
        "--config",
        default="configs/default.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--output-dir",
        default=str(EXPERIENCE_DIR),
        help="输出目录"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=32,
        help="推理批处理大小"
    )
    
    return parser.parse_args()


def load_static_model(model_path: str, device: torch.device) -> Tuple[Any, Dict]:
    """
    加载训练完成的静态模型
    
    Args:
        model_path: 模型文件路径
        device: 计算设备
        
    Returns:
        (模型, 配置信息)
    """
    print(f"📥 加载静态模型: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # 获取配置信息
    config = checkpoint.get('config', {})
    optimization_config = checkpoint.get('optimization_config', {})
    
    # 动态导入模型
    from src.model import ProceduralVideoUnderstanding
    
    # 创建模型实例
    model = ProceduralVideoUnderstanding(config).to(device)
    
    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"✅ 静态模型加载成功")
    print(f"   - 训练轮数: {checkpoint.get('epoch', 'unknown')}")
    print(f"   - 模型准确率: {checkpoint.get('accuracy', 'unknown'):.4f}")
    print(f"   - 参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    return model, config


def perform_static_inference(model: Any, test_loader, device: torch.device) -> Dict[str, Any]:
    """
    执行静态模型推理
    
    Args:
        model: 静态模型
        test_loader: 测试数据加载器
        device: 计算设备
        
    Returns:
        推理结果字典
    """
    print("🔍 执行静态模型推理...")
    
    model.eval()
    results = {
        'predictions': [],
        'ground_truth': [],
        'features': [],
        'prototype_distances': [],
        'task_graph_outputs': []
    }
    
    total_samples = 0
    correct_predictions = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            # 移动数据到设备
            for key in ["features", "labels"]:
                if key in batch:
                    batch[key] = batch[key].to(device, non_blocking=True)
            
            # 模型推理
            outputs = model(batch, mode="eval")
            
            # 收集结果
            predictions = torch.argmax(outputs["action_logits"], dim=-1)
            labels = batch["labels"]
            
            # 统计准确率
            valid_mask = labels >= 0
            if valid_mask.any():
                correct_predictions += (predictions[valid_mask] == labels[valid_mask]).sum().item()
                total_samples += valid_mask.sum().item()
            
            # 保存详细结果
            results['predictions'].extend(predictions.cpu().numpy().tolist())
            results['ground_truth'].extend(labels.cpu().numpy().tolist())
            
            # 保存特征和中间结果
            if 'prototype_distances' in outputs:
                results['prototype_distances'].extend(
                    outputs['prototype_distances'].cpu().numpy().tolist()
                )
            
            if batch_idx % 10 == 0:
                current_acc = correct_predictions / total_samples if total_samples > 0 else 0
                print(f"   Batch {batch_idx}: 当前准确率 = {current_acc:.4f}")
    
    # 计算最终指标
    final_accuracy = correct_predictions / total_samples if total_samples > 0 else 0
    
    results.update({
        'accuracy': final_accuracy,
        'total_samples': total_samples,
        'correct_predictions': correct_predictions
    })
    
    print(f"✅ 静态模型推理完成")
    print(f"   - 总样本数: {total_samples}")
    print(f"   - 准确率: {final_accuracy:.4f}")
    
    return results


def generate_static_analysis_report(results: Dict[str, Any], config: Dict, 
                                  model_path: str, output_dir: Path) -> None:
    """
    生成静态模型分析报告
    
    Args:
        results: 推理结果
        config: 模型配置
        model_path: 模型路径
        output_dir: 输出目录
    """
    print("📊 生成静态模型分析报告...")
    
    try:
        # 创建输出目录
        report_dir = output_dir / "reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成Markdown报告
        report_path = report_dir / f"static_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 静态任务图模型分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**模型路径**: {model_path}\n\n")
            
            f.write("## 模型性能指标\n\n")
            f.write(f"- **总样本数**: {results['total_samples']:,}\n")
            f.write(f"- **正确预测数**: {results['correct_predictions']:,}\n")
            f.write(f"- **准确率**: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)\n\n")
            
            f.write("## 模型配置信息\n\n")
            f.write(f"- **特征维度**: {config.get('data', {}).get('feature_dim', 'N/A')}\n")
            f.write(f"- **动作类别数**: {config.get('data', {}).get('num_actions', 'N/A')}\n")
            f.write(f"- **最大序列长度**: {config.get('data', {}).get('max_sequence_length', 'N/A')}\n")
            f.write(f"- **原型维度**: {config.get('model', {}).get('prototype', {}).get('prototype_dim', 'N/A')}\n")
            
            f.write("\n## 推理统计信息\n\n")
            f.write(f"- **预测样本数**: {len(results['predictions'])}\n")
            f.write(f"- **标签样本数**: {len(results['ground_truth'])}\n")
            
        # 保存JSON格式的详细结果
        json_path = report_dir / f"static_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({
                'model_path': model_path,
                'config': config,
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 静态模型分析报告已生成:")
        print(f"   📄 Markdown报告: {report_path}")
        print(f"   📋 JSON结果: {json_path}")
        
    except Exception as e:
        print(f"⚠️ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 静态任务图模型推理和分析")
    print("=" * 60)
    
    # 解析参数
    args = parse_args()
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        for directory in [REPORT_DIR, VIS_DIR]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 初始化设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🔧 使用设备: {device}")
        
        # 加载模型
        model, config = load_static_model(args.model_path, device)
        
        # 创建数据加载器
        print("🏗️ 创建数据加载器...")
        import yaml
        with open(args.config, "r", encoding="utf-8") as f:
            data_config = yaml.safe_load(f)
        
        # 更新批处理大小
        data_config["training"]["batch_size"] = args.batch_size
        
        from src.data_loader_fixed import create_data_loaders
        _, test_loader = create_data_loaders(data_config)
        
        # 执行推理
        results = perform_static_inference(model, test_loader, device)
        
        # 生成分析报告
        generate_static_analysis_report(results, config, args.model_path, output_dir)
        
        print(f"\n🎉 静态模型分析完成！")
        print(f"   输出目录: {output_dir}")
        
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
