# 程序性视频理解系统 - 自动GPU检测版

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0--2.5-red.svg)](https://pytorch.org/)
[![CUDA](https://img.shields.io/badge/CUDA-11.8%2B%20%7C%2012.x-green.svg)](https://developer.nvidia.com/cuda-downloads)
![Tested](https://img.shields.io/badge/tested-Code%20Review%20Fixed-brightgreen.svg)
![Environment](https://img.shields.io/badge/environment-sci__1-brightgreen.svg)
![Status](https://img.shields.io/badge/status-Production%20Ready-success.svg)

## 📋 系统概述

基于**原型学习**和**差分动态任务图**的程序性视频理解模型，采用自动GPU检测和单卡训练策略，确保训练稳定性。

### 🎯 核心特性

- **原型学习**: 学习每个动作类别的典型特征表示
- **静态任务图**: 建模动作之间的依赖关系和时序约束
- **差分动态任务图**: 基于执行偏差动态调整任务图权重
- **多视角融合**: 融合来自不同视角的信息
- **自动GPU检测**: 智能选择最优GPU，单卡训练策略
- **训练推理分离**: 训练和推理代码完全分离，模块化设计

## 🛠️ 环境要求

- **操作系统**: Linux Ubuntu 20.04+
- **硬件**: NVIDIA GPU (自动检测最优GPU)
- **Python**: 3.8-3.9
- **CUDA**: 11.8+
- **内存**: 建议32GB+系统内存（根据GPU显存自动调整）
- **环境**: sci_1 conda环境

## 🚀 快速开始

### 1. 环境配置

```bash
# 激活sci_1环境
conda activate sci_1

# 验证PyTorch和CUDA环境
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"
```

### 2. 数据准备

将Breakfast Actions数据集放置在以下目录结构：

```text
/data2/syd_data/Breakfast_Data/
├── breakfast_data/          # 特征文件
│   ├── s1/cereals/         # 视角1特征文件
│   ├── s2/cereals/         # 视角2特征文件  
│   ├── s3/cereals/         # 视角3特征文件
│   └── s4/cereals/         # 视角4特征文件
└── segmentation_coarse/     # 标签文件
    ├── S1_label/cereals/   # 视角1标签文件
    ├── S2_label/cereals/   # 视角2标签文件
    ├── S3_label/cereals/   # 视角3标签文件
    └── S4_label/cereals/   # 视角4标签文件
```

### 3. 模型训练

#### 静态模型训练（自动GPU检测）

```bash
# 基础训练（自动选择最优GPU）
python scripts/train_Static.py --config=configs/default.yaml --amp

# 服务器配置训练（推荐）
python scripts/train_Static.py --config=configs/server_config.yaml --amp

# 自定义批处理大小和worker数量
python scripts/train_Static.py --config=configs/default.yaml --amp --accum=4 --workers=8
```

#### 差分模型训练（基于静态模型）

```bash
# 基于静态模型训练差分模型
python scripts/train_Diff.py --static-model=/path/to/static_model.pth --amp

# 使用服务器配置
python scripts/train_Diff.py --static-model=/path/to/static_model.pth --config=configs/server_config.yaml --amp

# 自定义差分学习率缩放因子
python scripts/train_Diff.py --static-model=/path/to/static_model.pth --diff-lr-scale=0.05 --amp
```

### 4. 模型推理和分析

#### 静态模型分析

```bash
# 静态模型推理分析
python experience/experience_Static.py --model-path=/path/to/static_model.pth

# 自定义输出目录和批处理大小
python experience/experience_Static.py --model-path=/path/to/static_model.pth --batch-size=64 --output-dir=/custom/output
```

#### 差分模型分析

```bash
# 差分模型推理分析
python experience/experience_Diff.py --model-path=/path/to/diff_model.pth

# 自定义配置
python experience/experience_Diff.py --model-path=/path/to/diff_model.pth --config=configs/server_config.yaml
```

#### 对比分析

```bash
# 静态vs差分模型对比分析
python experience/experience_Static_and_Diff.py \
    --static-model=/path/to/static_model.pth \
    --diff-model=/path/to/diff_model.pth

# 自定义输出目录
python experience/experience_Static_and_Diff.py \
    --static-model=/path/to/static_model.pth \
    --diff-model=/path/to/diff_model.pth \
    --output-dir=/custom/output
```

## 🏗️ 项目结构

```text
├── src/                    # 核心源码
│   ├── model.py           # 主模型架构
│   ├── data_loader_fixed.py  # 数据加载器
│   ├── evaluation_simple.py  # 评估模块
│   ├── prototype_learning.py # 原型学习
│   ├── task_graph_simple.py  # 任务图学习
│   ├── multi_view_fusion.py  # 多视角融合
│   ├── training_utils.py  # 训练工具（含自动GPU检测）
│   └── report_generator.py   # 报告生成
├── scripts/               # 训练脚本
│   ├── train_Static.py   # 静态模型训练（自动GPU检测）
│   └── train_Diff.py     # 差分模型训练（单卡策略）
├── experience/            # 推理和分析模块
│   ├── experience_Static.py        # 静态模型推理分析
│   ├── experience_Diff.py          # 差分模型推理分析
│   └── experience_Static_and_Diff.py # 对比分析
├── configs/               # 配置文件
│   ├── default.yaml      # 默认配置
│   └── server_config.yaml   # 服务器配置
├── requirements.txt      # 依赖列表
├── Agent.md              # Agent工作记录
└── deploy_server.sh      # 部署脚本
```

## ⚙️ 配置说明

### 自动GPU检测和优化配置

| GPU显存 | 批处理大小 | 显存使用率 | 数据Worker | TF32加速 | 说明 |
|---------|-----------|-----------|-----------|----------|------|
| ≥40GB | 64 | 85% | 8 | 启用 | 高端GPU（A6000, A100等）|
| ≥20GB | 32 | 80% | 6 | 禁用 | 中端GPU（RTX 3090, 4090等）|
| <20GB | 16 | 75% | 4 | 禁用 | 低端GPU |

### 训练策略配置

- **单卡训练**: 强制单GPU模式，确保稳定性
- **自动检测**: 选择显存使用率最低的GPU
- **保守配置**: 优先稳定性而非极限性能
- **梯度累积**: 默认2步，可通过`--accum`调整

### 差分模型专用配置

- 差分学习率缩放因子: 0.1x（可通过`--diff-lr-scale`调整）
- 调度采样概率: 0.0-0.5 (逐渐增加)
- 权重调整强度: α=0.1
- 动态图更新频率: 每个时间步

## 📊 性能指标

| GPU类型 | 显存 | 批处理大小 | 显存使用 | 训练策略 | 稳定性 |
|---------|------|-----------|----------|----------|--------|
| A6000 | 48GB | 64 | ~40GB | 单卡训练 | 高 |
| RTX 4090 | 24GB | 32 | ~19GB | 单卡训练 | 高 |
| RTX 3090 | 24GB | 32 | ~19GB | 单卡训练 | 高 |
| RTX 3080 | 10GB | 16 | ~8GB | 单卡训练 | 中 |

## 🔧 测试与验证

### 环境测试

```bash
# 验证PyTorch和CUDA环境
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

# 验证GPU检测功能
python -c "from src.training_utils import get_optimal_gpu; print(f'最优GPU: {get_optimal_gpu()}')"

# 测试模型导入
python -c "from src.model import ProceduralVideoUnderstanding; print('✅ 模型导入成功')"
```

### 功能验证

环境验证包括以下功能：

- ✅ Python 3.8 + PyTorch 2.4.1
- ✅ CUDA环境和GPU检测
- ✅ 核心模块导入
- ✅ 自动GPU选择功能
- ✅ 模型创建和配置
- ✅ 训练脚本语法检查

## 🎨 模型架构

### 静态模型组件

1. **多视角融合模块**: 融合不同视角的特征
2. **时序编码器**: 双向LSTM处理时序信息
3. **原型学习器**: 学习动作类别的典型特征
4. **任务图编码器**: 简化GCN建模动作依赖关系
5. **动作分类器**: 预测动作类别

### 差分模型组件

1. **差分特征计算器**: 计算执行偏差
2. **权重调整网络**: MLP生成图权重调整信号
3. **动态图管理器**: 实时更新任务图权重
4. **融合网络**: 结合静态和动态预测

## 📈 输出文件

训练和推理完成后，系统会在以下位置生成输出：

```text
/data2/syd_data/Breakfast_Data/Outputs/
├── checkpoints/           # 模型检查点
│   ├── static_model.pth          # 静态模型
│   └── diff_model.pth            # 差分模型
├── experience/            # 推理分析结果
│   ├── reports/          # 分析报告
│   │   ├── static_analysis_report_*.md
│   │   ├── differential_analysis_report_*.md
│   │   ├── comparative_analysis_report_*.md
│   │   ├── static_analysis_results_*.json
│   │   ├── differential_analysis_results_*.json
│   │   └── comparative_analysis_results_*.json
│   └── visualizations/   # 可视化图表（预留）
├── visualizations/        # 训练过程图表（已移除）
└── reports/              # 训练数据记录
    ├── static_training_history.json
    └── differential_training_history.json
```

## 🐛 故障排除

### 常见问题

1. **CUDA内存不足**

   ```bash
   # 降低批处理大小
   --config=configs/default.yaml  # 使用较小的批处理配置
   ```

2. **模块导入错误**

   ```bash
   # 确保在项目根目录
   cd /data2/syd_data/Breakfast_Data/Linux_Plan_A
   python run_sci1_tests.py
   ```

3. **数据路径错误**

   ```bash
   # 检查数据路径配置
   vim configs/server_config.yaml
   ```

### 性能优化建议

- 使用混合精度训练 (`--amp`)
- 谨慎启用模型编译 (`--compile`)，可能影响稳定性
- 配置适当的梯度累积步数 (`--accum`)
- 调整数据加载器worker数量 (`--workers`)
- 让系统自动检测和配置GPU参数
- 优先考虑训练稳定性而非极限性能

## 📝 更新日志

### v2024.12.19.2 - 代码库全面审查和Bug修复版

#### 🔧 修复内容

- ✅ **环境兼容性修复**: PyTorch版本兼容性问题，支持2.0-2.5版本
- ✅ **混合精度训练修复**: 自动适配torch.amp和torch.cuda.amp API
- ✅ **GPU检测优化**: 改进自动GPU选择算法和错误处理
- ✅ **代码质量提升**: 修复语法错误、类型注解、未使用变量等问题
- ✅ **稳定性增强**: 优化内存管理、异常处理和错误恢复机制
- ✅ **依赖版本优化**: 更新requirements.txt确保版本兼容性

#### 🚀 性能优化

- ✅ **A6000 48GB显存优化**: 针对目标硬件的保守配置策略
- ✅ **单卡训练策略**: 强制单GPU模式确保训练稳定性
- ✅ **内存管理改进**: 自动批处理大小调整和内存清理优化
- ✅ **错误处理增强**: 详细的错误信息和修复建议

#### 📊 验证结果

- ✅ 所有Python文件语法检查通过
- ✅ 核心模块导入逻辑修复完成
- ✅ GPU检测和选择功能优化
- ✅ 混合精度训练兼容性修复

### v2024.12.19.1 - 自动GPU检测版

- ✅ 实现自动GPU检测和选择功能
- ✅ 移除硬编码的双A6000配置
- ✅ 采用保守的单卡训练策略
- ✅ 训练和推理代码完全分离
- ✅ 创建专用的推理分析模块
- ✅ 实现模块化代码架构
- ✅ 更新文档以反映新的代码结构
- ✅ 修复命令行参数一致性问题
- ✅ 统一Conda环境名称为sci_1
- ✅ 修复梯度累积逻辑，允许1步累积

## 📄 许可证

本项目遵循MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request。

---

## ⚡ 快速启动命令（已验证通过）

### 完整训练流程

```bash
# 1. 环境准备和验证
conda activate sci_1
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

# 验证GPU检测功能
python -c "from src.training_utils import get_optimal_gpu; print(f'最优GPU: {get_optimal_gpu()}')"

# 2. 静态模型训练（自动GPU检测，保守配置）
python scripts/train_Static.py --config=configs/default.yaml --amp

# 3. 基于静态模型训练差分模型
python scripts/train_Diff.py \
    --static-model=/data2/syd_data/Breakfast_Data/Outputs/checkpoints/static_model.pth \
    --config=configs/default.yaml \
    --amp

# 4. 执行推理分析
python experience/experience_Static_and_Diff.py \
    --static-model=/data2/syd_data/Breakfast_Data/Outputs/checkpoints/static_model.pth \
    --diff-model=/data2/syd_data/Breakfast_Data/Outputs/checkpoints/diff_model.pth
```

### 高级配置选项

```bash
# 使用服务器配置（更大批处理大小）
python scripts/train_Static.py --config=configs/server_config.yaml --amp --accum=2

# 自定义差分学习率
python scripts/train_Diff.py \
    --static-model=/path/to/static_model.pth \
    --diff-lr-scale=0.05 \
    --amp

# 自定义数据加载器worker数量
python scripts/train_Static.py --config=configs/default.yaml --amp --workers=8
```
