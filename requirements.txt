# ============================================================================
# 程序性视频理解项目依赖文件 (Procedural Video Understanding Dependencies)
# ============================================================================
#
# 本文件包含项目运行所需的所有Python依赖包
# 支持统一训练启动器和跨平台硬件优化
#
# 安装方法：
# 1. 创建conda环境: conda create -n sci_1 python=3.8 -y
# 2. 激活环境: conda activate sci_1
# 3. 安装依赖: pip install -r requirements.txt
# 4. 安装PyTorch (CUDA): conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
#
# 版本说明：
# - 所有版本号基于实际测试环境验证
# - 支持Python 3.8-3.11
# - 兼容CUDA 11.8和12.x
# - 专为Linux服务器环境优化
# ============================================================================

# ----------------------------------------------------------------------------
# 核心深度学习框架 (Core Deep Learning Frameworks)
# ----------------------------------------------------------------------------
# PyTorch生态系统 - 兼容CUDA 11.8和12.x，支持GPU加速和混合精度训练
# 注意：推荐使用conda安装以获得最佳CUDA支持
# conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
# 或者：conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
torch>=2.0.0,<2.5.0              # 深度学习框架，稳定版本范围
torchvision>=0.15.0,<0.20.0      # 计算机视觉工具包，与torch版本匹配
torchaudio>=2.0.0,<2.5.0         # 音频处理工具包（可选），与torch版本匹配

# ----------------------------------------------------------------------------
# 数值计算和科学计算 (Numerical and Scientific Computing)
# ----------------------------------------------------------------------------
numpy>=1.21.0,<3.0.0             # 基础数值计算库
scipy>=1.7.0,<2.0.0              # 科学计算库，提供优化和统计功能
scikit-learn>=1.0.0,<2.0.0       # 机器学习算法库，用于评估指标

# ----------------------------------------------------------------------------
# 数据可视化 (Data Visualization)
# ----------------------------------------------------------------------------
matplotlib>=3.5.0,<4.0.0         # 基础绘图库，支持中文字体
seaborn>=0.11.0,<1.0.0           # 统计可视化库，美化图表

# ----------------------------------------------------------------------------
# 图处理和网络分析 (Graph Processing and Network Analysis)
# ----------------------------------------------------------------------------
# 注意：使用轻量级networkx替代torch-geometric以减少依赖复杂性
networkx>=2.6.0,<4.0.0           # 图论和网络分析库，用于任务图建模

# ----------------------------------------------------------------------------
# 配置文件和数据处理 (Configuration and Data Processing)
# ----------------------------------------------------------------------------
pyyaml>=6.0,<7.0.0               # YAML配置文件解析
pandas>=1.3.0,<3.0.0             # 数据处理和分析（可选）

# ----------------------------------------------------------------------------
# 进度条和用户界面 (Progress Bars and User Interface)
# ----------------------------------------------------------------------------
tqdm>=4.62.0,<5.0.0              # 进度条显示，训练过程可视化

# ----------------------------------------------------------------------------
# 开发和调试工具 (Development and Debugging Tools)
# ----------------------------------------------------------------------------
# 以下包用于开发和调试，生产环境可选
ipython>=7.0.0,<9.0.0            # 交互式Python环境（开发用）
jupyter>=1.0.0,<2.0.0            # Jupyter notebook支持（开发用）

# ----------------------------------------------------------------------------
# 性能优化和监控 (Performance Optimization and Monitoring)
# ----------------------------------------------------------------------------
psutil>=5.8.0,<6.0.0             # 系统资源监控，GPU内存管理
memory-profiler>=0.60.0,<1.0.0   # 内存使用分析（调试用）

# ----------------------------------------------------------------------------
# 图像和视频处理 (Image and Video Processing)
# ----------------------------------------------------------------------------
# 注意：这些依赖是可选的，仅在需要额外的图像处理功能时安装
# Pillow>=8.0.0,<11.0.0          # 图像处理库（可选）
# opencv-python>=4.5.0,<5.0.0    # 计算机视觉库（可选）

# ----------------------------------------------------------------------------
# 服务器环境专用依赖 (Server Environment Dependencies)
# ----------------------------------------------------------------------------
# 以下依赖用于服务器环境的高性能训练
tensorboard>=2.8.0,<3.0.0       # 训练监控和可视化
tensorboardX>=2.4,<3.0.0        # TensorBoard扩展
wandb>=0.12.0,<1.0.0            # 实验跟踪（可选）

# 多GPU训练和优化
# flash-attn>=2.0.0              # Flash Attention优化（Linux专用，可选）
# apex                           # NVIDIA Apex优化（可选）

# ----------------------------------------------------------------------------
# Linux环境专用依赖 (Linux-specific Dependencies)
# ----------------------------------------------------------------------------
packaging>=21.0,<24.0           # 版本解析和包管理

# ----------------------------------------------------------------------------
# 特殊说明 (Special Notes)
# ----------------------------------------------------------------------------
#
# 1. PyTorch安装：
#    推荐使用conda安装PyTorch以获得最佳CUDA支持：
#    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
#
# 2. 自动GPU检测训练启动器：
#    - 使用 scripts/train_Static.py --config=configs/server_config.yaml --amp
#    - 使用 scripts/train_Diff.py --static-model=/path/to/model.pth --config=configs/server_config.yaml --amp
#    - 自动检测GPU硬件并应用专用优化配置
#    - 专为Linux服务器环境优化
#
# 3. 中文字体支持：
#    项目包含自动中文字体检测和配置功能，无需额外安装
#
# 4. 版本兼容性：
#    - Python: 3.8-3.11 (推荐3.8或3.9)
#    - CUDA: 11.8或12.x
#    - 操作系统: Ubuntu 18.04+, CentOS 7+
#
# 5. 可选依赖：
#    标记为"可选"的包可以根据实际需求选择性安装
#    核心功能不依赖这些包
#
# ============================================================================
