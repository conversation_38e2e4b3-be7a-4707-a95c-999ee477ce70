#!/usr/bin/env python3
"""
差分模型训练启动器 - 自动GPU检测和单卡训练
基于静态模型训练框架，专门用于差分模型的训练
支持Linux服务器环境下的稳定训练
"""

import argparse
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

# PyTorch相关导入 - 修复版本兼容性问题
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F

    # 修复混合精度训练导入问题
    GradScaler = None
    autocast = None

    try:
        # 尝试新的API (PyTorch 2.0+)
        from torch.amp import GradScaler, autocast
        print(f"✅ PyTorch {torch.__version__} - 使用新的混合精度API")
    except ImportError:
        try:
            # 回退到旧的API (PyTorch 1.x)
            from torch.cuda.amp import GradScaler, autocast
            print(f"✅ PyTorch {torch.__version__} - 使用旧的混合精度API")
        except ImportError:
            print("⚠️ 混合精度训练不可用，将使用标准精度")

    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")
    print("请确保已正确安装PyTorch：")
    print("conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia")
    TORCH_AVAILABLE = False
    GradScaler = None
    autocast = None

# 项目根目录设置
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# 确保工作目录正确
if os.getcwd() != str(ROOT_DIR):
    os.chdir(ROOT_DIR)

# 输出目录配置 - Linux服务器环境
def get_output_dir():
    """Linux服务器环境输出目录"""
    return Path("/data2/syd_data/Breakfast_Data/Outputs")

OUTPUT_DIR = get_output_dir()
CHECKPOINT_DIR = OUTPUT_DIR / "checkpoints"
VIS_DIR = OUTPUT_DIR / "visualizations"
REPORT_DIR = OUTPUT_DIR / "reports"

def parse_cli() -> argparse.Namespace:
    """解析命令行参数 - 差分模型专用"""
    parser = argparse.ArgumentParser(
        description="差分模型训练启动器 - 自动GPU检测和单卡训练"
    )
    parser.add_argument(
        "--config",
        default="configs/default.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--static-model",
        required=True,
        help="预训练静态模型的路径"
    )
    parser.add_argument(
        "--compile",
        action="store_true",
        help="启用torch.compile优化（可能不稳定）"
    )
    parser.add_argument(
        "--amp",
        action="store_true",
        default=True,
        help="启用自动混合精度训练"
    )
    parser.add_argument(
        "--accum",
        type=int,
        default=2,
        help="梯度累积步数"
    )
    parser.add_argument(
        "--workers",
        type=str,
        default="auto",
        help="数据加载器worker数量，可以是数字或'auto'"
    )
    parser.add_argument(
        "--diff-lr-scale",
        type=float,
        default=0.1,
        help="差分模型学习率缩放因子"
    )

    return parser.parse_args()


# 平台兼容性和设备初始化函数已移至 training_utils.py


# 使用通用训练工具中的硬件优化函数
from src.training_utils import (
    init_device_and_distributed,
    setup_hardware_optimization,
    create_optimizer,
    setup_model_compilation,
    setup_distributed_model,
    cleanup_gpu_memory,
    set_random_seed,
    worker_init_fn
)


def load_static_model(static_model_path: str, config: Dict, device: torch.device):
    """加载预训练的静态模型"""
    print(f"📥 加载静态模型: {static_model_path}")

    if not os.path.exists(static_model_path):
        raise FileNotFoundError(f"静态模型文件不存在: {static_model_path}")

    # 动态导入模型
    from src.model import ProceduralVideoUnderstanding

    # 创建模型实例
    static_model = ProceduralVideoUnderstanding(config).to(device)

    # 加载预训练权重
    checkpoint = torch.load(static_model_path, map_location=device, weights_only=False)

    if 'model_state_dict' in checkpoint:
        static_model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 成功加载静态模型权重 (训练轮数: {checkpoint.get('epoch', 'unknown')})")
        print(f"   模型准确率: {checkpoint.get('accuracy', 'unknown'):.4f}")
    else:
        static_model.load_state_dict(checkpoint)
        print("✅ 成功加载静态模型权重")

    # 冻结静态模型参数
    for param in static_model.parameters():
        param.requires_grad = False

    static_model.eval()
    return static_model


def load_and_update_config(config_path: str, optimization_config: Dict[str, Any], args: argparse.Namespace) -> Dict:
    """加载并更新配置文件（差分模型专用）"""
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)

    # 应用硬件优化配置
    if optimization_config["device_type"] == "cuda":
        config["training"]["batch_size"] = optimization_config["batch_size"]
        config["training"]["num_workers"] = optimization_config["num_workers"]

        # 差分模型特殊配置
        original_lr = config["training"]["learning_rate"]
        config["training"]["learning_rate"] = original_lr * args.diff_lr_scale

        # 更新GPU优化配置
        gpu_opt = config["training"].setdefault("gpu_optimization", {})
        gpu_opt.update({
            "enable_amp": True,
            "num_workers": optimization_config["num_workers"],
            "pin_memory": True,
            "max_memory_fraction": optimization_config["memory_fraction"],
            "memory_cleanup_freq": optimization_config["cleanup_freq"],
            "enable_tf32": optimization_config.get("enable_tf32", False),
            "enable_compile": optimization_config.get("enable_compile", False),
            "persistent_workers": True,
            "prefetch_factor": 3,  # 差分模型使用较小的预取因子
        })

        print(f"✅ 差分模型配置已更新为{optimization_config['profile']}优化")
        print(f"   - 批处理大小: {config['training']['batch_size']}")
        print(f"   - 数据加载器worker: {config['training']['num_workers']}")
        print(f"   - 显存使用比例: {optimization_config['memory_fraction']*100:.0f}%")
        print(f"   - 差分学习率: {config['training']['learning_rate']:.6f} (缩放因子: {args.diff_lr_scale})")

    return config


class DifferentialFeatureCalculator(nn.Module):
    """差分特征计算模块"""

    def __init__(self, feature_dim: int, prototype_dim: int, diff_dim: int = 256):
        super().__init__()
        self.feature_dim = feature_dim
        self.prototype_dim = prototype_dim
        self.diff_dim = diff_dim

        # 特征投影层
        self.feature_projector = nn.Sequential(
            nn.Linear(feature_dim, prototype_dim),
            nn.ReLU(),
            nn.LayerNorm(prototype_dim)
        )

        # 差分计算网络
        self.diff_calculator = nn.Sequential(
            nn.Linear(prototype_dim * 2, diff_dim),  # 当前特征 + 原型特征
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(diff_dim, diff_dim),
            nn.LayerNorm(diff_dim)
        )

    def forward(self, video_features: torch.Tensor, current_action: torch.Tensor,
                prototype_features: torch.Tensor) -> torch.Tensor:
        """
        计算差分特征

        Args:
            video_features: [batch_size, seq_len, feature_dim] 视频特征
            current_action: [batch_size, seq_len] 当前动作标签
            prototype_features: [num_actions, prototype_dim] 动作原型特征

        Returns:
            diff_features: [batch_size, seq_len, diff_dim] 差分特征
        """
        batch_size, seq_len, _ = video_features.shape
        device = video_features.device

        # 确保所有张量在同一设备上
        prototype_features = prototype_features.to(device)
        current_action = current_action.to(device)

        # 投影视频特征到原型空间
        projected_features = self.feature_projector(video_features)  # [batch_size, seq_len, prototype_dim]

        # 获取对应动作的原型特征
        action_prototypes = prototype_features[current_action]  # [batch_size, seq_len, prototype_dim]

        # 计算差分特征：当前执行与预期执行的偏差
        combined_features = torch.cat([projected_features, action_prototypes], dim=-1)
        diff_features = self.diff_calculator(combined_features)

        return diff_features


class DifferentialWeightAdjustmentMLP(nn.Module):
    """差分权重调整网络 (MLP_diff)"""

    def __init__(self, diff_dim: int, num_actions: int, hidden_dim: int = 512):
        super().__init__()
        self.diff_dim = diff_dim
        self.num_actions = num_actions
        self.hidden_dim = hidden_dim

        # MLP网络：将执行偏差转换为图权重调整信号
        self.mlp = nn.Sequential(
            nn.Linear(diff_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, num_actions),  # 输出权重调整向量
            nn.Tanh()  # 限制调整幅度在[-1, 1]
        )

        # 调整强度控制
        self.adjustment_scale = nn.Parameter(torch.tensor(0.1))

    def forward(self, diff_features: torch.Tensor) -> torch.Tensor:
        """
        生成权重调整向量

        Args:
            diff_features: [batch_size, seq_len, diff_dim] 差分特征

        Returns:
            delta_W_raw: [batch_size, seq_len, num_actions] 原始权重调整向量
        """
        delta_W_raw = self.mlp(diff_features)
        delta_W_raw = delta_W_raw * self.adjustment_scale
        return delta_W_raw


class DynamicGraphManager(nn.Module):
    """动态图管理器"""

    def __init__(self, num_actions: int, alpha: float = 0.1):
        super().__init__()
        self.num_actions = num_actions
        self.alpha = alpha

        # 初始化图权重矩阵（可训练参数）
        # 注意：将图权重定义为参数，以便在优化器中更新
        self.W_current = nn.Parameter(torch.eye(num_actions))

    def update_weights(self, W_prev: torch.Tensor, delta_W_raw: torch.Tensor,
                      current_action: torch.Tensor, alpha: Optional[float] = None) -> torch.Tensor:
        """
        更新权重矩阵

        Args:
            W_prev: [num_actions, num_actions] 前一时刻的权重矩阵
            delta_W_raw: [batch_size, seq_len, num_actions] 权重调整向量
            current_action: [batch_size, seq_len] 当前动作
            alpha: 更新强度

        Returns:
            W_updated: [batch_size, seq_len, num_actions, num_actions] 更新后的权重矩阵
        """
        if alpha is None:
            alpha = self.alpha

        batch_size, seq_len, _ = delta_W_raw.shape
        device = delta_W_raw.device

        # 确保所有张量在同一设备上
        W_prev = W_prev.to(device)
        current_action = current_action.to(device)

        # 扩展W_prev到batch维度
        W_batch = W_prev.unsqueeze(0).unsqueeze(0).expand(batch_size, seq_len, -1, -1).clone()

        # 对每个时间步和批次更新对应动作的权重行
        for b in range(batch_size):
            for t in range(seq_len):
                action_idx = current_action[b, t].item()
                if action_idx >= 0:  # 忽略填充标签
                    # 更新第action_idx行的权重
                    W_batch[b, t, action_idx, :] += alpha * delta_W_raw[b, t, :]

        return W_batch

    def get_graph_prediction(self, W_current: torch.Tensor, current_action: torch.Tensor) -> torch.Tensor:
        """
        基于当前图权重生成下一步动作概率分布

        Args:
            W_current: [batch_size, seq_len, num_actions, num_actions] 当前权重矩阵
            current_action: [batch_size, seq_len] 当前动作

        Returns:
            P_graph: [batch_size, seq_len, num_actions] 下一步动作概率分布
        """
        batch_size, seq_len, _, _ = W_current.shape
        device = W_current.device

        # 确保current_action在正确的设备上
        current_action = current_action.to(device)

        P_graph = torch.zeros(batch_size, seq_len, self.num_actions, device=device)

        for b in range(batch_size):
            for t in range(seq_len):
                action_idx = current_action[b, t].item()
                if action_idx >= 0:  # 忽略填充标签
                    # 提取第action_idx行并应用Softmax
                    weights = W_current[b, t, action_idx, :]
                    P_graph[b, t, :] = F.softmax(weights, dim=0)

        return P_graph


class DifferentialModel(nn.Module):
    """完整的差分模型"""

    def __init__(self, static_model, config: Dict):
        super().__init__()
        self.static_model = static_model
        self.config = config

        # 冻结静态模型参数
        for param in self.static_model.parameters():
            param.requires_grad = False

        # 差分模型组件
        model_config = config["model"]
        data_config = config["data"]

        self.diff_feature_calculator = DifferentialFeatureCalculator(
            feature_dim=data_config["feature_dim"],
            prototype_dim=model_config["prototype"]["prototype_dim"],
            diff_dim=256
        )

        self.diff_weight_mlp = DifferentialWeightAdjustmentMLP(
            diff_dim=256,
            num_actions=data_config["num_actions"],
            hidden_dim=512
        )

        self.dynamic_graph_manager = DynamicGraphManager(
            num_actions=data_config["num_actions"],
            alpha=0.1
        )

        # 融合网络：结合静态预测和动态图预测
        self.fusion_network = nn.Sequential(
            nn.Linear(data_config["num_actions"] * 2, data_config["num_actions"]),
            nn.ReLU(),
            nn.Linear(data_config["num_actions"], data_config["num_actions"])
        )

    def forward(self, batch: Dict[str, torch.Tensor], mode: str = "train",
                scheduled_sampling_prob: float = 0.0) -> Dict[str, torch.Tensor]:
        """
        差分模型前向传播

        Args:
            batch: 输入批次数据
            mode: 模式 ['train', 'eval', 'test']
            scheduled_sampling_prob: 训练时使用预测结果而非真实标签的概率 (0.0-1.0)
                                   用于缓解Exposure Bias问题
        """
        # 1. 获取静态模型输出
        with torch.no_grad():
            static_outputs = self.static_model(batch, mode=mode)

        features = batch["features"]
        labels = batch["labels"]
        device = features.device

        # 2. 获取动作原型特征并确保在正确设备上
        prototype_centers = self.static_model.prototype_learner.compute_prototype_centers()
        prototype_centers = prototype_centers.to(device)

        # 3. 计算差分特征 - 修复Exposure Bias问题
        if mode == "train" and scheduled_sampling_prob > 0.0:
            # 训练时使用调度采样策略：随机选择使用真实标签或预测结果
            batch_size, seq_len = labels.shape
            use_prediction_mask = torch.rand(batch_size, seq_len, device=device) < scheduled_sampling_prob

            # 创建混合的current_action：部分使用真实标签，部分使用预测结果
            current_action = torch.where(
                use_prediction_mask,
                static_outputs["action_predictions"],
                labels
            )
        else:
            # 标准策略：训练时使用真实标签，评估时使用预测结果
            current_action = labels if mode == "train" else static_outputs["action_predictions"]

        diff_features = self.diff_feature_calculator(
            video_features=features,
            current_action=current_action,
            prototype_features=prototype_centers
        )

        # 4. 生成权重调整向量
        delta_W_raw = self.diff_weight_mlp(diff_features)

        # 5. 动态更新图权重
        W_prev = self.dynamic_graph_manager.W_current.to(device)
        W_updated = self.dynamic_graph_manager.update_weights(
            W_prev=W_prev,
            delta_W_raw=delta_W_raw,
            current_action=current_action
        )

        # 6. 生成动态图预测
        P_graph_dynamic = self.dynamic_graph_manager.get_graph_prediction(
            W_current=W_updated,
            current_action=current_action
        )

        # 7. 融合静态预测和动态预测
        static_probs = F.softmax(static_outputs["action_logits"], dim=-1)
        combined_probs = torch.cat([static_probs, P_graph_dynamic], dim=-1)
        fused_logits = self.fusion_network(combined_probs)
        fused_predictions = torch.argmax(fused_logits, dim=-1)

        # 构建输出
        outputs = {
            # 差分模型输出
            "action_logits": fused_logits,
            "action_predictions": fused_predictions,
            # 静态模型输出（用于对比）
            "static_action_logits": static_outputs["action_logits"],
            "static_action_predictions": static_outputs["action_predictions"],
            # 动态图输出
            "dynamic_graph_logits": torch.log(P_graph_dynamic + 1e-8),
            "dynamic_graph_predictions": torch.argmax(P_graph_dynamic, dim=-1),
            # 中间结果
            "diff_features": diff_features,
            "delta_W_raw": delta_W_raw,
            "W_updated": W_updated,
            "P_graph_dynamic": P_graph_dynamic,
            # 继承其他输出
            **{k: v for k, v in static_outputs.items() if k not in ["action_logits", "action_predictions"]}
        }

        return outputs

    def compute_loss(self, outputs: Dict[str, torch.Tensor], batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """计算差分模型损失"""
        labels = batch["labels"]
        valid_mask = labels >= 0

        losses = {}

        if valid_mask.any():
            valid_labels = labels[valid_mask]

            # 1. 主要分类损失（融合预测）
            valid_fused_logits = outputs["action_logits"][valid_mask]
            fused_loss = F.cross_entropy(valid_fused_logits, valid_labels)
            losses["fused_classification"] = fused_loss * 1.0

            # 2. 静态模型损失（用于对比）
            valid_static_logits = outputs["static_action_logits"][valid_mask]
            static_loss = F.cross_entropy(valid_static_logits, valid_labels)
            losses["static_classification"] = static_loss * 0.5

            # 3. 动态图损失
            valid_dynamic_logits = outputs["dynamic_graph_logits"][valid_mask]
            dynamic_loss = F.cross_entropy(valid_dynamic_logits, valid_labels)
            losses["dynamic_graph"] = dynamic_loss * 0.3

            # 4. 权重调整正则化损失
            delta_W_raw = outputs["delta_W_raw"]
            regularization_loss = torch.mean(torch.abs(delta_W_raw))
            losses["weight_regularization"] = regularization_loss * 0.01

        # 总损失
        total_loss = sum(losses.values())
        losses["total_loss"] = total_loss

        return losses


def create_diff_model(static_model, config: Dict, device: torch.device):
    """创建差分模型（基于静态模型）"""
    print("🏗️ 创建差分模型...")

    # 创建完整的差分模型
    diff_model = DifferentialModel(static_model, config).to(device)

    print("✅ 差分模型创建成功")
    print(f"   - 差分特征计算器: {sum(p.numel() for p in diff_model.diff_feature_calculator.parameters()):,} 参数")
    print(f"   - 权重调整MLP: {sum(p.numel() for p in diff_model.diff_weight_mlp.parameters()):,} 参数")
    print(f"   - 融合网络: {sum(p.numel() for p in diff_model.fusion_network.parameters()):,} 参数")
    print(f"   - 总新增参数: {sum(p.numel() for p in diff_model.parameters() if p.requires_grad):,}")

    return diff_model


def save_differential_training_data(train_history: Dict, config: Dict, optimization_config: Dict,
                                   best_accuracy: float, model_path: Path, static_model_path: str,
                                   diff_lr_scale: float) -> None:
    """
    保存差分模型训练数据 - 移除可视化功能

    Args:
        train_history: 训练历史记录
        config: 训练配置
        optimization_config: 硬件优化配置
        best_accuracy: 最佳准确率
        model_path: 差分模型保存路径
        static_model_path: 静态模型路径
        diff_lr_scale: 差分学习率缩放因子
    """
    try:
        import json
        from datetime import datetime
        import os

        # 保存JSON格式的训练历史
        history_path = REPORT_DIR / "differential_training_history.json"
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump({
                'train_history': train_history,
                'config': config,
                'optimization_config': optimization_config,
                'best_accuracy': best_accuracy,
                'model_path': str(model_path),
                'static_model_path': static_model_path,
                'diff_lr_scale': diff_lr_scale,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)

        print(f"✅ 差分模型训练数据已保存:")
        print(f"   📋 历史数据: {history_path}")
        print(f"   💾 模型文件: {model_path}")
        print(f"   🎯 最佳准确率: {best_accuracy:.4f}")
        print(f"   📊 基于静态模型: {os.path.basename(static_model_path)}")

    except Exception as e:
        print(f"⚠️ 差分模型训练数据保存失败: {e}")
        import traceback
        traceback.print_exc()


def create_optimizer(model: nn.Module, config: Dict, optimization_config: Dict) -> torch.optim.Optimizer:
    """创建优化器（差分模型专用）"""
    lr = config["training"]["learning_rate"]
    weight_decay = config["training"]["weight_decay"]

    # Linux专用优化器选择
    platform_name = "Linux"

    # 尝试使用FusedAdam优化器（可选依赖）
    try:
        # 首先尝试flash-attention的FusedAdam
        from flash_attn.optim.fused_adam import FusedAdam
        optimizer = FusedAdam(model.parameters(), lr=lr, weight_decay=weight_decay)
        print("✅ 使用FusedAdam优化器 (flash-attn) - 差分模型")
        return optimizer
    except ImportError:
        try:
            # 回退到apex的FusedAdam
            from apex.optimizers import FusedAdam
            optimizer = FusedAdam(model.parameters(), lr=lr, weight_decay=weight_decay)
            print("✅ 使用FusedAdam优化器 (apex) - 差分模型")
            return optimizer
        except ImportError:
            pass

    # 回退到标准Adam
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    print("✅ 使用标准Adam优化器 - 差分模型")
    return optimizer


def setup_model_compilation(model: nn.Module, args: argparse.Namespace,
                          optimization_config: Dict) -> Any:
    """设置模型编译优化（差分模型专用）"""
    if args.compile and optimization_config.get("enable_compile", False):
        try:
            # 检查torch.compile可用性
            if hasattr(torch, "compile"):
                compiled_model = torch.compile(model, backend="inductor", mode="reduce-overhead")
                print("✅ 启用torch.compile优化 - 差分模型")
                return compiled_model
            else:
                print("⚠️ torch.compile不可用，跳过编译优化")
        except Exception as e:
            print(f"⚠️ torch.compile失败: {e}")

    return model


def setup_distributed_model(model: nn.Module, optimization_config: Dict) -> nn.Module:
    """设置分布式模型包装（差分模型专用）- 单卡训练模式"""
    # 强制单卡训练模式，不进行分布式包装
    print("📝 差分模型单卡训练模式：跳过分布式模型包装")
    return model


def cleanup_gpu_memory(optimization_config: Dict) -> None:
    """清理GPU内存"""
    if optimization_config["device_type"] == "cuda":
        torch.cuda.empty_cache()
        if torch.cuda.device_count() > 1:
            torch.cuda.synchronize()


if __name__ == "__main__":
    print("🚀 差分模型训练启动器 - 自动GPU检测")
    print("=" * 60)

    # 解析命令行参数
    args = parse_cli()
    print(f"📋 平台: Linux")
    print(f"📋 训练策略: 单卡训练")
    print(f"📋 静态模型: {args.static_model}")

    try:
        # 创建输出目录
        for directory in [CHECKPOINT_DIR, VIS_DIR, REPORT_DIR]:
            directory.mkdir(parents=True, exist_ok=True)

        # 初始化设备（自动选择最优GPU）
        device = init_device_and_distributed("auto")

        # 设置硬件优化（基于检测到的GPU）
        optimization_config = setup_hardware_optimization("auto", device)

        # 加载配置
        config = load_and_update_config(args.config, optimization_config, args)

        # 设置随机种子确保实验可复现性
        seed = config.get("experiment", {}).get("seed", 42)
        from src.training_utils import set_random_seed, worker_init_fn
        set_random_seed(seed, deterministic=True)

        # 动态导入训练模块
        print("\n📦 导入训练模块...")
        from src.data_loader_fixed import create_data_loaders
        from src.evaluation_simple import Evaluator

        # 创建数据加载器
        print("🏗️ 创建数据加载器...")
        train_loader, test_loader = create_data_loaders(config, worker_init_fn=worker_init_fn)

        # 根据训练数据集更新动作数量，确保模型配置正确
        num_actions = len(train_loader.dataset.action_to_id)
        config["data"]["num_actions"] = num_actions
        print(f"✅ 动作类别数更新为: {num_actions}")

        # 加载静态模型
        static_model = load_static_model(args.static_model, config, device)

        # 创建差分模型
        diff_model = create_diff_model(static_model, config, device)

        # 模型编译优化
        diff_model = setup_model_compilation(diff_model, args, optimization_config)

        # 分布式模型包装
        diff_model = setup_distributed_model(diff_model, optimization_config)

        # 创建优化器
        optimizer = create_optimizer(diff_model, config, optimization_config)

        # 创建学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=config["training"]["num_epochs"]
        )

        # 混合精度训练 - 修复版本兼容性
        scaler = None
        if args.amp and device.type == "cuda" and GradScaler is not None:
            try:
                scaler = GradScaler()
                print("✅ 启用混合精度训练 - 差分模型")
            except Exception as e:
                print(f"⚠️ 混合精度训练初始化失败: {e}")
                scaler = None
        elif args.amp:
            print("⚠️ 混合精度训练不可用，使用标准精度 - 差分模型")

        # 创建评估器
        evaluator = Evaluator(config)

        print(f"\n🎯 开始差分模型训练 (自动GPU检测配置)")
        print(f"   批处理大小: {config['training']['batch_size']}")
        print(f"   训练轮数: {config['training']['num_epochs']}")
        print(f"   差分学习率: {config['training']['learning_rate']:.6f}")
        print("\n✅ 差分模型架构已实现:")
        print("   - 差分特征计算模块")
        print("   - 差分权重调整网络 (MLP_diff)")
        print("   - 动态图管理器")
        print("   - 静态vs差分性能对比机制")

        # 训练循环
        best_accuracy = 0.0
        train_history = {
            "epoch": [],
            "train_loss": [],
            "train_accuracy": [],
            "test_accuracy": []
        }

        for epoch in range(config["training"]["num_epochs"]):
            print(f"\n📈 Epoch {epoch+1}/{config['training']['num_epochs']}")

            # 为分布式训练设置epoch（确保数据混洗的随机性）
            if hasattr(train_loader, 'sampler') and hasattr(train_loader.sampler, 'set_epoch'):
                train_loader.sampler.set_epoch(epoch)
                print(f"🔄 已为分布式采样器设置epoch: {epoch}")

            # 计算调度采样概率 - 缓解Exposure Bias
            # 随着训练进行，逐渐增加使用预测结果的概率
            total_epochs = config["training"]["num_epochs"]
            scheduled_sampling_prob = min(0.5, epoch / total_epochs * 0.5)  # 最大0.5
            print(f"🎯 调度采样概率: {scheduled_sampling_prob:.3f}")

            # 训练一个epoch
            diff_model.train()
            epoch_loss = 0.0
            epoch_correct = 0
            epoch_total = 0

            cleanup_freq = optimization_config.get("cleanup_freq", 200)
            accum_steps = max(1, args.accum)

            for batch_idx, batch in enumerate(train_loader):
                # 移动数据到设备
                for key in ["features", "labels"]:
                    if key in batch:
                        batch[key] = batch[key].to(device, non_blocking=True)

                # 梯度清零 - 修复：在累积周期开始时清零梯度
                if batch_idx % accum_steps == 0:
                    optimizer.zero_grad(set_to_none=True)

                if args.amp and scaler is not None and autocast is not None:
                    # 混合精度训练
                    with autocast():
                        outputs = diff_model(batch, mode="train",
                                           scheduled_sampling_prob=scheduled_sampling_prob)
                        losses = diff_model.compute_loss(outputs, batch)
                        total_loss = losses["total_loss"] / accum_steps

                    scaler.scale(total_loss).backward()

                    if (batch_idx + 1) % accum_steps == 0:
                        scaler.step(optimizer)
                        scaler.update()
                else:
                    # 标准训练
                    outputs = diff_model(batch, mode="train",
                                       scheduled_sampling_prob=scheduled_sampling_prob)
                    losses = diff_model.compute_loss(outputs, batch)
                    total_loss = losses["total_loss"] / accum_steps

                    total_loss.backward()

                    if (batch_idx + 1) % accum_steps == 0:
                        optimizer.step()

                # 统计
                epoch_loss += total_loss.item() * accum_steps
                predictions = torch.argmax(outputs["action_logits"], dim=-1)
                labels = batch["labels"]
                epoch_correct += (predictions == labels).sum().item()
                epoch_total += labels.numel()

                # 定期清理内存
                if batch_idx % cleanup_freq == 0:
                    cleanup_gpu_memory(optimization_config)

                # 显示进度
                if batch_idx % 20 == 0:
                    current_acc = epoch_correct / epoch_total if epoch_total > 0 else 0
                    print(f"   Batch {batch_idx}: Loss={total_loss.item():.4f}, Acc={current_acc:.4f}")

            # 计算训练指标
            train_metrics = {
                "loss": epoch_loss / len(train_loader),
                "accuracy": epoch_correct / epoch_total if epoch_total > 0 else 0
            }

            # 评估差分模型性能
            diff_model.eval()
            with torch.no_grad():
                test_results = evaluator.evaluate(diff_model, test_loader, device)

            # 更新学习率
            scheduler.step()

            # 记录历史
            train_history["epoch"].append(epoch + 1)
            train_history["train_loss"].append(train_metrics["loss"])
            train_history["train_accuracy"].append(train_metrics["accuracy"])
            train_history["test_accuracy"].append(test_results["frame_accuracy"])

            print(f"   训练损失: {train_metrics['loss']:.4f}")
            print(f"   训练准确率: {train_metrics['accuracy']:.4f}")
            print(f"   测试准确率: {test_results['frame_accuracy']:.4f}")

            # 保存最佳模型
            if test_results["frame_accuracy"] > best_accuracy:
                best_accuracy = test_results["frame_accuracy"]
                checkpoint_path = CHECKPOINT_DIR / "diff_model.pth"
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': diff_model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': best_accuracy,
                    'config': config,
                    'optimization_config': optimization_config,
                    'static_model_path': args.static_model,
                    'train_history': train_history
                }, checkpoint_path)
                print(f"   💾 保存最佳差分模型 (准确率: {best_accuracy:.4f})")

        print(f"\n🎉 差分模型训练完成！")
        print(f"   最佳准确率: {best_accuracy:.4f}")
        print(f"   模型保存位置: {CHECKPOINT_DIR}")

        # 保存差分模型训练数据
        print("\n📊 保存差分模型训练数据...")
        save_differential_training_data(
            train_history=train_history,
            config=config,
            optimization_config=optimization_config,
            best_accuracy=best_accuracy,
            model_path=CHECKPOINT_DIR / "diff_model.pth",
            static_model_path=args.static_model,
            diff_lr_scale=args.diff_lr_scale
        )

    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


